<Window x:Class="FinancialTracker.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:FinancialTracker"
        Title="Financial Tracker"
        Height="800"
        Width="1400"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Top Bar -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Finance" Width="32" Height="32" Margin="0,0,8,0"/>
                    <TextBlock Text="Financial Tracker" FontSize="20" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="DashboardButton" Content="Dashboard" Style="{StaticResource MaterialDesignFlatButton}"
                            Foreground="White" Margin="8,0" Click="DashboardButton_Click"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1">
            <Grid Margin="16">
                <!-- Dashboard Content -->
                <Grid x:Name="DashboardContent" Visibility="Visible">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Header -->
                    <TextBlock Grid.Row="0" Text="Projects Overview" FontSize="20" FontWeight="Bold" Margin="0,0,0,12"/>

                    <!-- PO Financial Summary Cards -->
                    <Grid Grid.Row="1" Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:Card Grid.Column="0" Margin="6" Padding="16" Background="#E3F2FD" MinHeight="90">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="FolderMultiple" Width="24" Height="24" Foreground="#1976D2" Margin="0,0,0,8"/>
                                <TextBlock Text="Total Projects" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalProjectsText" Text="0" FontSize="24" FontWeight="Bold" Foreground="#1976D2" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="1" Margin="6" Padding="16" Background="#E8F5E8" MinHeight="90">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="24" Height="24" Foreground="#388E3C" Margin="0,0,0,8"/>
                                <TextBlock Text="Total PO Value" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalPOValueText" Text="$0" FontSize="24" FontWeight="Bold" Foreground="#388E3C" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="2" Margin="6" Padding="16" Background="#FFF3E0" MinHeight="90">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="TrendingUp" Width="24" Height="24" Foreground="#F57C00" Margin="0,0,0,8"/>
                                <TextBlock Text="Total Spent" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalSpentText" Text="$0" FontSize="24" FontWeight="Bold" Foreground="#F57C00" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="3" Margin="6" Padding="16" Background="#FFEBEE" MinHeight="90">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="TrendingDown" Width="24" Height="24" Foreground="#D32F2F" Margin="0,0,0,8"/>
                                <TextBlock Text="Total Remaining" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalRemainingText" Text="$0" FontSize="24" FontWeight="Bold" Foreground="#D32F2F" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>





                    <!-- Projects Details - Takes remaining space -->
                    <materialDesign:Card Grid.Row="3" Margin="4" Padding="12">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <!-- Header - Enhanced with better spacing -->
                            <Grid Grid.Row="0" Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                                    <TextBlock Text="Financial Projects Summary" FontSize="20" FontWeight="Bold" Margin="0,0,0,4"/>
                                    <TextBlock Text="Complete overview of all project finances including PO, Tasks, and Services breakdown"
                                               FontSize="12" Opacity="0.7"/>
                                </StackPanel>
                                <Button Grid.Column="1" Style="{StaticResource MaterialDesignRaisedButton}"
                                        FontSize="13" Padding="16,8" Click="AddProjectButton_Click">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,6,0"/>
                                        <TextBlock Text="Add New Project"/>
                                    </StackPanel>
                                </Button>
                            </Grid>

                            <!-- Search and Filter Section - Compact -->
                            <materialDesign:Card Grid.Row="1" Margin="0,0,0,8" Padding="0">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header -->
                                    <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="16,12">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                                <materialDesign:PackIcon Kind="FilterVariant" Width="20" Height="20" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                                <TextBlock Text="Search and Filter" FontSize="14" FontWeight="Medium" VerticalAlignment="Center"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                <Button x:Name="ClearFiltersButton"
                                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="0,0,8,0" Padding="12,6" FontSize="12"
                                                        Click="ClearFiltersButton_Click">
                                                    <StackPanel Orientation="Horizontal">
                                                        <materialDesign:PackIcon Kind="FilterRemove" Width="16" Height="16" Margin="0,0,4,0"/>
                                                        <TextBlock Text="Clear Filters"/>
                                                    </StackPanel>
                                                </Button>

                                                <Button x:Name="RefreshButton" Style="{StaticResource MaterialDesignIconButton}"
                                                        ToolTip="Refresh Data" Click="RefreshButton_Click">
                                                    <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18"/>
                                                </Button>
                                            </StackPanel>
                                        </Grid>
                                    </materialDesign:ColorZone>

                                    <!-- Filter Controls - Better width utilization -->
                                    <Grid Grid.Row="1" Margin="16,12">
                                        <!-- Search Box with better spacing -->
                                        <StackPanel HorizontalAlignment="Center" MaxWidth="800">
                                            <TextBlock Text="Search Projects" FontSize="13" FontWeight="Medium" Margin="0,0,0,6" Opacity="0.8" HorizontalAlignment="Center"/>
                                            <Grid>
                                                <TextBox x:Name="SearchTextBox" materialDesign:HintAssist.Hint="Type project name or description to search..."
                                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                         Padding="45,12,12,12" MinWidth="600" FontSize="13"
                                                         TextChanged="SearchTextBox_TextChanged"/>
                                                <materialDesign:PackIcon Kind="Magnify" Width="22" Height="22"
                                                                       HorizontalAlignment="Left" VerticalAlignment="Center"
                                                                       Margin="15,0,0,0" Opacity="0.6" />
                                            </Grid>
                                        </StackPanel>
                                    </Grid>
                                </Grid>
                            </materialDesign:Card>
                            <!-- Projects Table - Excel-like design with full width utilization -->
                            <DataGrid Grid.Row="2" x:Name="ProjectsSummaryDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                      IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="All"
                                      HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                                      BorderBrush="#E0E0E0" BorderThickness="1"
                                      ColumnWidth="*" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto">
                                <DataGrid.Resources>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#F5F5F5"/>
                                        <Setter Property="Foreground" Value="#333"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="FontSize" Value="12"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderBrush" Value="#E0E0E0"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                        <Setter Property="Padding" Value="10,8"/>
                                        <Setter Property="Height" Value="40"/>
                                    </Style>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="BorderBrush" Value="#E0E0E0"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                        <Setter Property="Padding" Value="6,4"/>
                                        <Setter Property="FontSize" Value="12"/>
                                        <Setter Property="Height" Value="35"/>
                                    </Style>
                                    <Style TargetType="DataGridRow">
                                        <Setter Property="Height" Value="35"/>
                                    </Style>
                                </DataGrid.Resources>
                                <DataGrid.Columns>
                                    <!-- Project Name Column - Wider for better readability -->
                                    <DataGridTextColumn Header="Project Name" Binding="{Binding Name}" Width="2*" MinWidth="180">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Left"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- PO Section - Better spacing -->
                                    <DataGridTextColumn Header="PO Total" Binding="{Binding POAmount, StringFormat='{}{0:C0}'}" Width="*" MinWidth="110">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#1976D2"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="12"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="PO Spent" Binding="{Binding TotalSpent, StringFormat='{}{0:C0}'}" Width="*" MinWidth="110">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#F57C00"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="12"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="PO Remaining" Binding="{Binding PORemaining, StringFormat='{}{0:C0}'}" Width="*" MinWidth="110">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#D32F2F"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="12"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Tasks Section - Better spacing -->
                                    <DataGridTextColumn Header="Tasks Total" Binding="{Binding TaskCommitmentsAmount, StringFormat='{}{0:C0}'}" Width="*" MinWidth="110">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#4CAF50"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="12"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Tasks Spent" Binding="{Binding TasksSpent, StringFormat='{}{0:C0}'}" Width="*" MinWidth="110">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#FF9800"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="12"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Tasks Remaining" Binding="{Binding TasksRemaining, StringFormat='{}{0:C0}'}" Width="*" MinWidth="110">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#E91E63"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="12"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Services Section - Better spacing -->
                                    <DataGridTextColumn Header="Services Total" Binding="{Binding ServiceCommitmentsAmount, StringFormat='{}{0:C0}'}" Width="*" MinWidth="110">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#9C27B0"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="12"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Services Spent" Binding="{Binding ServicesSpent, StringFormat='{}{0:C0}'}" Width="*" MinWidth="110">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#FF5722"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="12"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Services Remaining" Binding="{Binding ServicesRemaining, StringFormat='{}{0:C0}'}" Width="*" MinWidth="110">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#795548"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="12"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Status Column -->
                                    <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="*" MinWidth="90">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="12"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Actions Column - Wider for better button spacing -->
                                    <DataGridTemplateColumn Header="Actions" Width="2*" MinWidth="320">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                    <Button Content="Details" Style="{StaticResource MaterialDesignRaisedButton}"
                                                            Margin="3" Padding="10,6" FontSize="11" Click="ViewProjectDetailsButton_Click"/>
                                                    <Button Content="Edit" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="3" Padding="10,6" FontSize="11" Click="EditProjectButton_Click"/>
                                                    <Button Content="Delete" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="3" Padding="10,6" FontSize="11" Click="DeleteProjectButton_Click"/>
                                                    <Button Content="Files" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="3" Padding="10,6" FontSize="11" Click="ViewProjectFilesButton_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>

                            <!-- Summary Statistics Section -->
                            <materialDesign:Card Grid.Row="3" Margin="0,16,0,0" Padding="20">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header -->
                                    <TextBlock Grid.Row="0" Text="Financial Summary Overview" FontSize="18" FontWeight="Bold"
                                               Margin="0,0,0,20" HorizontalAlignment="Center"/>

                                    <!-- Main Financial Cards -->
                                    <Grid Grid.Row="1" Margin="0,0,0,20">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- PO Summary Card -->
                                        <materialDesign:Card Grid.Column="0" Margin="0,0,10,0" Padding="20" Background="#E3F2FD">
                                            <StackPanel HorizontalAlignment="Center">
                                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="40" Height="40"
                                                                       Foreground="#1976D2" Margin="0,0,0,10"/>
                                                <TextBlock Text="Purchase Orders" FontSize="16" FontWeight="Bold"
                                                         HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                    </Grid.RowDefinitions>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Total:" FontWeight="Medium" Margin="0,0,10,5"/>
                                                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="SummaryPOTotalText" Text="$0"
                                                             FontWeight="Bold" FontSize="14" Foreground="#1976D2" HorizontalAlignment="Right"/>

                                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Spent:" FontWeight="Medium" Margin="0,0,10,5"/>
                                                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="SummaryPOSpentText" Text="$0"
                                                             FontWeight="Bold" FontSize="14" Foreground="#F57C00" HorizontalAlignment="Right"/>

                                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Remaining:" FontWeight="Medium" Margin="0,0,10,0"/>
                                                    <TextBlock Grid.Row="2" Grid.Column="1" x:Name="SummaryPORemainingText" Text="$0"
                                                             FontWeight="Bold" FontSize="14" Foreground="#D32F2F" HorizontalAlignment="Right"/>
                                                </Grid>
                                            </StackPanel>
                                        </materialDesign:Card>

                                        <!-- Tasks Summary Card -->
                                        <materialDesign:Card Grid.Column="1" Margin="5,0" Padding="20" Background="#E8F5E8">
                                            <StackPanel HorizontalAlignment="Center">
                                                <materialDesign:PackIcon Kind="CheckboxMarkedCircle" Width="40" Height="40"
                                                                       Foreground="#4CAF50" Margin="0,0,0,10"/>
                                                <TextBlock Text="Tasks &amp; Hardware" FontSize="16" FontWeight="Bold"
                                                         HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                    </Grid.RowDefinitions>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Total:" FontWeight="Medium" Margin="0,0,10,5"/>
                                                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="SummaryTasksTotalText" Text="$0"
                                                             FontWeight="Bold" FontSize="14" Foreground="#4CAF50" HorizontalAlignment="Right"/>

                                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Spent:" FontWeight="Medium" Margin="0,0,10,5"/>
                                                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="SummaryTasksSpentText" Text="$0"
                                                             FontWeight="Bold" FontSize="14" Foreground="#FF9800" HorizontalAlignment="Right"/>

                                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Remaining:" FontWeight="Medium" Margin="0,0,10,0"/>
                                                    <TextBlock Grid.Row="2" Grid.Column="1" x:Name="SummaryTasksRemainingText" Text="$0"
                                                             FontWeight="Bold" FontSize="14" Foreground="#E91E63" HorizontalAlignment="Right"/>
                                                </Grid>
                                            </StackPanel>
                                        </materialDesign:Card>

                                        <!-- Services Summary Card -->
                                        <materialDesign:Card Grid.Column="2" Margin="10,0,0,0" Padding="20" Background="#F3E5F5">
                                            <StackPanel HorizontalAlignment="Center">
                                                <materialDesign:PackIcon Kind="Cog" Width="40" Height="40"
                                                                       Foreground="#9C27B0" Margin="0,0,0,10"/>
                                                <TextBlock Text="Services" FontSize="16" FontWeight="Bold"
                                                         HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                    </Grid.RowDefinitions>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Total:" FontWeight="Medium" Margin="0,0,10,5"/>
                                                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="SummaryServicesTotalText" Text="$0"
                                                             FontWeight="Bold" FontSize="14" Foreground="#9C27B0" HorizontalAlignment="Right"/>

                                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Spent:" FontWeight="Medium" Margin="0,0,10,5"/>
                                                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="SummaryServicesSpentText" Text="$0"
                                                             FontWeight="Bold" FontSize="14" Foreground="#FF5722" HorizontalAlignment="Right"/>

                                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Remaining:" FontWeight="Medium" Margin="0,0,10,0"/>
                                                    <TextBlock Grid.Row="2" Grid.Column="1" x:Name="SummaryServicesRemainingText" Text="$0"
                                                             FontWeight="Bold" FontSize="14" Foreground="#795548" HorizontalAlignment="Right"/>
                                                </Grid>
                                            </StackPanel>
                                        </materialDesign:Card>
                                    </Grid>

                                    <!-- Additional Statistics -->
                                    <Grid Grid.Row="2">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Project Count -->
                                        <materialDesign:Card Grid.Column="0" Margin="0,0,5,0" Padding="15" Background="#FFF3E0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <materialDesign:PackIcon Kind="FolderMultiple" Width="30" Height="30"
                                                                       Foreground="#FF9800" Margin="0,0,0,8"/>
                                                <TextBlock Text="Active Projects" FontSize="12" FontWeight="Medium"
                                                         HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                                <TextBlock x:Name="SummaryProjectCountText" Text="0" FontSize="20" FontWeight="Bold"
                                                         Foreground="#FF9800" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </materialDesign:Card>

                                        <!-- Total Commitments -->
                                        <materialDesign:Card Grid.Column="1" Margin="5,0" Padding="15" Background="#E8F5E8">
                                            <StackPanel HorizontalAlignment="Center">
                                                <materialDesign:PackIcon Kind="FileDocument" Width="30" Height="30"
                                                                       Foreground="#4CAF50" Margin="0,0,0,8"/>
                                                <TextBlock Text="Total Commitments" FontSize="12" FontWeight="Medium"
                                                         HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                                <TextBlock x:Name="SummaryCommitmentsCountText" Text="0" FontSize="20" FontWeight="Bold"
                                                         Foreground="#4CAF50" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </materialDesign:Card>

                                        <!-- Total Invoices -->
                                        <materialDesign:Card Grid.Column="2" Margin="5,0" Padding="15" Background="#E3F2FD">
                                            <StackPanel HorizontalAlignment="Center">
                                                <materialDesign:PackIcon Kind="Receipt" Width="30" Height="30"
                                                                       Foreground="#2196F3" Margin="0,0,0,8"/>
                                                <TextBlock Text="Total Invoices" FontSize="12" FontWeight="Medium"
                                                         HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                                <TextBlock x:Name="SummaryInvoicesCountText" Text="0" FontSize="20" FontWeight="Bold"
                                                         Foreground="#2196F3" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </materialDesign:Card>

                                        <!-- Overall Progress -->
                                        <materialDesign:Card Grid.Column="3" Margin="5,0,0,0" Padding="15" Background="#F3E5F5">
                                            <StackPanel HorizontalAlignment="Center">
                                                <materialDesign:PackIcon Kind="ChartLine" Width="30" Height="30"
                                                                       Foreground="#9C27B0" Margin="0,0,0,8"/>
                                                <TextBlock Text="Overall Progress" FontSize="12" FontWeight="Medium"
                                                         HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                                <TextBlock x:Name="SummaryProgressText" Text="0%" FontSize="20" FontWeight="Bold"
                                                         Foreground="#9C27B0" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </materialDesign:Card>
                                    </Grid>
                                </Grid>
                            </materialDesign:Card>
                        </Grid>
                    </materialDesign:Card>
                </Grid>




            </Grid>
        </ScrollViewer>

        <!-- Status Bar -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="16,4">
            <Grid>
                <TextBlock Text="Ready" VerticalAlignment="Center"/>
                <TextBlock Text="v1.0.0" HorizontalAlignment="Right" VerticalAlignment="Center"/>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</Window>

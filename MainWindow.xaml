<Window x:Class="FinancialTracker.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:FinancialTracker"
        Title="Financial Tracker"
        Height="800"
        Width="1400"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Top Bar -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Finance" Width="32" Height="32" Margin="0,0,8,0"/>
                    <TextBlock Text="Financial Tracker" FontSize="20" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="DashboardButton" Content="Dashboard" Style="{StaticResource MaterialDesignFlatButton}"
                            Foreground="White" Margin="8,0" Click="DashboardButton_Click"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1">
            <Grid Margin="16">
                <!-- Dashboard Content -->
                <Grid x:Name="DashboardContent" Visibility="Visible">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Header -->
                    <TextBlock Grid.Row="0" Text="Projects Overview" FontSize="20" FontWeight="Bold" Margin="0,0,0,12"/>

                    <!-- PO Financial Summary Cards -->
                    <Grid Grid.Row="1" Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:Card Grid.Column="0" Margin="6" Padding="16" Background="#E3F2FD" MinHeight="90">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="FolderMultiple" Width="24" Height="24" Foreground="#1976D2" Margin="0,0,0,8"/>
                                <TextBlock Text="Total Projects" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalProjectsText" Text="0" FontSize="24" FontWeight="Bold" Foreground="#1976D2" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="1" Margin="6" Padding="16" Background="#E8F5E8" MinHeight="90">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="24" Height="24" Foreground="#388E3C" Margin="0,0,0,8"/>
                                <TextBlock Text="Total PO Value" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalPOValueText" Text="$0" FontSize="24" FontWeight="Bold" Foreground="#388E3C" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="2" Margin="6" Padding="16" Background="#FFF3E0" MinHeight="90">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="TrendingUp" Width="24" Height="24" Foreground="#F57C00" Margin="0,0,0,8"/>
                                <TextBlock Text="Total Spent" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalSpentText" Text="$0" FontSize="24" FontWeight="Bold" Foreground="#F57C00" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="3" Margin="6" Padding="16" Background="#FFEBEE" MinHeight="90">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="TrendingDown" Width="24" Height="24" Foreground="#D32F2F" Margin="0,0,0,8"/>
                                <TextBlock Text="Total Remaining" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalRemainingText" Text="$0" FontSize="24" FontWeight="Bold" Foreground="#D32F2F" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>





                    <!-- Projects Details - Takes remaining space -->
                    <materialDesign:Card Grid.Row="3" Margin="4" Padding="12">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <!-- Header - Compact design -->
                            <Grid Grid.Row="0" Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Financial Projects Summary" FontSize="18" FontWeight="Bold"
                                          VerticalAlignment="Center"/>
                                <Button Grid.Column="1" Style="{StaticResource MaterialDesignRaisedButton}"
                                        FontSize="12" Padding="12,6" Click="AddProjectButton_Click">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Plus" Width="14" Height="14" Margin="0,0,4,0"/>
                                        <TextBlock Text="Add Project"/>
                                    </StackPanel>
                                </Button>
                            </Grid>

                            <!-- Search Section - Compact -->
                            <materialDesign:Card Grid.Row="1" Margin="0,0,0,6" Padding="12">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Search Box -->
                                    <Grid Grid.Column="0">
                                        <TextBox x:Name="SearchTextBox" materialDesign:HintAssist.Hint="Search projects..."
                                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                 Padding="35,8,8,8" FontSize="12" Height="36"
                                                 TextChanged="SearchTextBox_TextChanged"/>
                                        <materialDesign:PackIcon Kind="Magnify" Width="18" Height="18"
                                                               HorizontalAlignment="Left" VerticalAlignment="Center"
                                                               Margin="10,0,0,0" Opacity="0.6" />
                                    </Grid>

                                    <!-- Clear Button -->
                                    <Button Grid.Column="1" x:Name="ClearFiltersButton"
                                            Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Margin="8,0" Padding="8,4" FontSize="11" Height="36"
                                            Click="ClearFiltersButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FilterRemove" Width="14" Height="14" Margin="0,0,3,0"/>
                                            <TextBlock Text="Clear"/>
                                        </StackPanel>
                                    </Button>

                                    <!-- Refresh Button -->
                                    <Button Grid.Column="2" x:Name="RefreshButton" Style="{StaticResource MaterialDesignIconButton}"
                                            ToolTip="Refresh Data" Width="36" Height="36" Click="RefreshButton_Click">
                                        <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16"/>
                                    </Button>
                                </Grid>
                            </materialDesign:Card>
                            <!-- Projects Table - Professional design with larger content -->
                            <DataGrid Grid.Row="2" x:Name="ProjectsSummaryDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                      IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="All"
                                      HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                                      BorderBrush="#E0E0E0" BorderThickness="1"
                                      ColumnWidth="*" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto">
                                <DataGrid.Resources>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#F8F9FA"/>
                                        <Setter Property="Foreground" Value="#2C3E50"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="FontSize" Value="13"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderBrush" Value="#E0E0E0"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                        <Setter Property="Padding" Value="12,10"/>
                                        <Setter Property="Height" Value="45"/>
                                    </Style>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="BorderBrush" Value="#E0E0E0"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                        <Setter Property="Padding" Value="8,6"/>
                                        <Setter Property="FontSize" Value="13"/>
                                        <Setter Property="Height" Value="42"/>
                                    </Style>
                                    <Style TargetType="DataGridRow">
                                        <Setter Property="Height" Value="42"/>
                                        <Setter Property="Background" Value="White"/>
                                    </Style>
                                    <Style TargetType="DataGridRow" x:Key="AlternatingRowStyle">
                                        <Setter Property="Background" Value="#FAFBFC"/>
                                    </Style>
                                </DataGrid.Resources>
                                <DataGrid.Columns>
                                    <!-- Project Name Column - Wider for better readability -->
                                    <DataGridTextColumn Header="Project Name" Binding="{Binding Name}" Width="2*" MinWidth="200">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Left"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="12,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- PO Section - Enhanced visibility -->
                                    <DataGridTextColumn Header="PO Total" Binding="{Binding POAmount, StringFormat='{}{0:C0}'}" Width="*" MinWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#1976D2"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="10,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="PO Spent" Binding="{Binding TotalSpent, StringFormat='{}{0:C0}'}" Width="*" MinWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#F57C00"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="10,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="PO Remaining" Binding="{Binding PORemaining, StringFormat='{}{0:C0}'}" Width="*" MinWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#D32F2F"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="10,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Tasks Section - Enhanced visibility -->
                                    <DataGridTextColumn Header="Tasks Total" Binding="{Binding TaskCommitmentsAmount, StringFormat='{}{0:C0}'}" Width="*" MinWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#4CAF50"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="10,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Tasks Spent" Binding="{Binding TasksSpent, StringFormat='{}{0:C0}'}" Width="*" MinWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#FF9800"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="10,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Tasks Remaining" Binding="{Binding TasksRemaining, StringFormat='{}{0:C0}'}" Width="*" MinWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#E91E63"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="10,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Services Section - Enhanced visibility -->
                                    <DataGridTextColumn Header="Services Total" Binding="{Binding ServiceCommitmentsAmount, StringFormat='{}{0:C0}'}" Width="*" MinWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#9C27B0"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="10,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Services Spent" Binding="{Binding ServicesSpent, StringFormat='{}{0:C0}'}" Width="*" MinWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#FF5722"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="10,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Services Remaining" Binding="{Binding ServicesRemaining, StringFormat='{}{0:C0}'}" Width="*" MinWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#795548"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="10,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Status Column -->
                                    <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="*" MinWidth="100">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Margin" Value="10,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Actions Column - Professional button layout -->
                                    <DataGridTemplateColumn Header="Actions" Width="2.5*" MinWidth="400">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                    <Button Content="Details" Style="{StaticResource MaterialDesignRaisedButton}"
                                                            Margin="5,3" Padding="14,8" FontSize="12" FontWeight="Medium"
                                                            MinWidth="75" Height="34" Click="ViewProjectDetailsButton_Click"/>
                                                    <Button Content="Edit" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="5,3" Padding="14,8" FontSize="12" FontWeight="Medium"
                                                            MinWidth="65" Height="34" Click="EditProjectButton_Click"/>
                                                    <Button Content="Delete" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="5,3" Padding="14,8" FontSize="12" FontWeight="Medium"
                                                            MinWidth="70" Height="34" Click="DeleteProjectButton_Click"/>
                                                    <Button Content="Files" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="5,3" Padding="14,8" FontSize="12" FontWeight="Medium"
                                                            MinWidth="65" Height="34" Click="ViewProjectFilesButton_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </materialDesign:Card>
                </Grid>




            </Grid>
        </ScrollViewer>

        <!-- Status Bar -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="16,4">
            <Grid>
                <TextBlock Text="Ready" VerticalAlignment="Center"/>
                <TextBlock Text="v1.0.0" HorizontalAlignment="Right" VerticalAlignment="Center"/>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</Window>

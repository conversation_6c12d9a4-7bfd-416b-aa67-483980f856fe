using System;
using System.IO;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace FinancialTracker.Data
{
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<FinancialContext>
    {
        public FinancialContext CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<FinancialContext>();
            var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "FinancialTracker.db");
            optionsBuilder.UseSqlite($"Data Source={dbPath}");
            
            return new FinancialContext(optionsBuilder.Options);
        }
    }
}

<Window x:Class="FinancialTracker.Views.MultiSelectDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="اختيار متعدد" Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Background="{DynamicResource MaterialDesignToolBarBackground}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="CheckboxMultipleMarked" Width="24" Height="24" 
                                       Foreground="White" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Name="txtTitle" Text="اختيار متعدد" FontSize="18" FontWeight="Bold" 
                          Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Search -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                    <materialDesign:PackIcon Kind="Search" Width="20" Height="20" 
                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBox Name="txtSearch" Style="{StaticResource MaterialDesignTextBox}" 
                            Width="300" materialDesign:HintAssist.Hint="البحث..."
                            TextChanged="TxtSearch_TextChanged"/>
                </StackPanel>

                <!-- Items List -->
                <ListBox Name="lstItems" Grid.Row="1" 
                        Style="{StaticResource MaterialDesignListBox}"
                        SelectionMode="Multiple">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <CheckBox Content="{Binding DisplayText}" 
                                     IsChecked="{Binding IsSelected}"
                                     Margin="4"/>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>

                <!-- Selection Info -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,16,0,0">
                    <materialDesign:PackIcon Kind="Information" Width="16" Height="16" 
                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock Name="txtSelectionInfo" Text="لم يتم اختيار أي عنصر" 
                              FontSize="12" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Buttons -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="btnSelectAll" Content="اختيار الكل" 
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Width="100" Margin="8,0" Click="BtnSelectAll_Click"/>
                <Button Name="btnDeselectAll" Content="إلغاء الكل" 
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Width="100" Margin="8,0" Click="BtnDeselectAll_Click"/>
                <Button Name="btnOK" Content="موافق" 
                        Style="{StaticResource MaterialDesignRaisedAccentButton}"
                        Width="100" Margin="8,0" Click="BtnOK_Click"/>
                <Button Name="btnCancel" Content="إلغاء" 
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Width="100" Margin="8,0" Click="BtnCancel_Click"/>
            </StackPanel>
        </Border>

    </Grid>
</Window>

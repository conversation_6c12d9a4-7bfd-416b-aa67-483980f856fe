using System;

namespace FinancialTracker.Helpers
{
    public static class MathHelper
    {
        /// <summary>
        /// Round the number according to the required rule:
        /// - If the decimal part >= 0.5, round up
        /// - If the decimal part < 0.5, just remove the decimal part
        /// </summary>
        /// <param name="value">The value to be rounded</param>
        /// <returns>The value rounded according to the rule</returns>
        public static decimal RoundCustom(decimal value)
        {
            // Get the integer part and decimal part
            decimal integerPart = Math.Floor(value);
            decimal decimalPart = value - integerPart;

            // If the decimal part >= 0.5, round up
            if (decimalPart >= 0.5m)
            {
                return integerPart + 1;
            }

            // If the decimal part < 0.5, just remove the decimal part
            return integerPart;
        }
        
        /// <summary>
        /// Round the number according to the required rule (double version)
        /// </summary>
        /// <param name="value">The value to be rounded</param>
        /// <returns>The value rounded according to the rule</returns>
        public static double RoundCustom(double value)
        {
            return (double)RoundCustom((decimal)value);
        }

        /// <summary>
        /// Round the number according to the required rule (float version)
        /// </summary>
        /// <param name="value">The value to be rounded</param>
        /// <returns>The value rounded according to the rule</returns>
        public static float RoundCustom(float value)
        {
            return (float)RoundCustom((decimal)value);
        }
    }
}

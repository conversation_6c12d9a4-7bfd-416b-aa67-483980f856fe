@echo off
echo ========================================
echo Financial Tracker - Development Run
echo ========================================
echo.

echo Checking for .NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK is not installed or not in PATH
    echo.
    echo Please install .NET 6.0 SDK from:
    echo https://dotnet.microsoft.com/download
    echo.
    echo After installation, restart Command Prompt and try again.
    pause
    exit /b 1
)

echo .NET SDK found! Running application...
dotnet run
pause

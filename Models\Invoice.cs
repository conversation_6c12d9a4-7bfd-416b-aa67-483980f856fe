#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FinancialTracker.Helpers;

namespace FinancialTracker.Models
{
    public class Invoice
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string InvoiceNumber { get; set; } = string.Empty;
        
        [Required]
        public int ProjectId { get; set; }

        public int? CommitmentId { get; set; }
        
        private decimal _amountUSD;

        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountUSD
        {
            get => MathHelper.RoundCustom(_amountUSD);
            set => _amountUSD = value;
        }
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        public DateTime? SignatureDate { get; set; }
        
        [Column(TypeName = "decimal(10,4)")]
        public decimal ExchangeRate { get; set; } = 1.0m;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public bool IsPaid { get; set; } = false;
        
        public DateTime? PaidDate { get; set; }

        // Partial payment support
        private decimal _paidAmount = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount
        {
            get => MathHelper.RoundCustom(_paidAmount);
            set => _paidAmount = value;
        }

        [MaxLength(50)]
        public string Type { get; set; } = "Software Tasks";

        [MaxLength(500)]
        public string? AttachedFilePath { get; set; }

        [MaxLength(100)]
        public string? AttachedFileName { get; set; }

        // Letter attachment properties
        [MaxLength(500)]
        public string? LetterFilePath { get; set; }

        [MaxLength(100)]
        public string? LetterFileName { get; set; }
        
        // Navigation properties
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;

        [ForeignKey("CommitmentId")]
        public virtual Commitment? Commitment { get; set; }

        public virtual ICollection<Reply> Replies { get; set; } = new List<Reply>();

        // Computed properties
        [NotMapped]
        public decimal RemainingAmount => MathHelper.RoundCustom(AmountUSD - PaidAmount);

        [NotMapped]
        public bool IsPartiallyPaid => PaidAmount > 0 && PaidAmount < AmountUSD;

        [NotMapped]
        public bool IsFullyPaid => PaidAmount >= AmountUSD;



        // Property to display type correctly
        [NotMapped]
        public string TypeDisplay => GetTypeDisplay();

        private string GetTypeDisplay()
        {
            // If type is a number, convert it to text
            if (int.TryParse(Type, out int typeNumber))
            {
                return typeNumber switch
                {
                    1 => "Software Tasks",
                    2 => "Hardware Tasks",
                    3 => "Services",
                    _ => "Other"
                };
            }

            // If type is text, return as is or convert to English
            return Type.ToLower() switch
            {
                "task" => "Tasks",
                "service" => "Services",
                _ => Type
            };
        }

        [NotMapped]
        public string PaymentStatus
        {
            get
            {
                if (PaidAmount == 0) return "Unpaid";
                if (PaidAmount >= AmountUSD) return "Fully Paid";
                return "Partially Paid";
            }
        }

        [NotMapped]
        public bool IsSigned => SignatureDate.HasValue;

        [NotMapped]
        public string SignatureStatus => IsSigned ? "Signed" : "Unsigned";
    }
}

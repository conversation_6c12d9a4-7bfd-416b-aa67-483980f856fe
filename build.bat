@echo off
echo ========================================
echo Financial Tracker - Build Script
echo ========================================
echo.

echo [1/4] Cleaning previous builds...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "publish" rmdir /s /q "publish"

echo [2/4] Restoring NuGet packages...
dotnet restore

echo [3/4] Building application...
dotnet build --configuration Release

echo [4/4] Publishing self-contained executable...
dotnet publish --configuration Release --self-contained true --runtime win-x64 --output ./publish --property:PublishSingleFile=true

echo.
echo BUILD COMPLETED SUCCESSFULLY!
echo Your application is ready in the 'publish' folder
pause

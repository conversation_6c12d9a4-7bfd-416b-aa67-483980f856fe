using System;

namespace FinancialTracker.Helpers
{
    public static class MathHelper
    {
        /// <summary>
        /// تقريب الرقم حسب القاعدة المطلوبة:
        /// - إذا كان الجزء العشري >= 0.5 يتم التقريب لأعلى
        /// - إذا كان الجزء العشري < 0.5 يتم حذف الجزء العشري فقط
        /// </summary>
        /// <param name="value">القيمة المراد تقريبها</param>
        /// <returns>القيمة مقربة حسب القاعدة</returns>
        public static decimal RoundCustom(decimal value)
        {
            // الحصول على الجزء الصحيح والجزء العشري
            decimal integerPart = Math.Floor(value);
            decimal decimalPart = value - integerPart;
            
            // إذا كان الجزء العشري >= 0.5، نقرب لأعلى
            if (decimalPart >= 0.5m)
            {
                return integerPart + 1;
            }
            
            // إذا كان الجزء العشري < 0.5، نحذف الجزء العشري فقط
            return integerPart;
        }
        
        /// <summary>
        /// تقريب الرقم حسب القاعدة المطلوبة (نسخة double)
        /// </summary>
        /// <param name="value">القيمة المراد تقريبها</param>
        /// <returns>القيمة مقربة حسب القاعدة</returns>
        public static double RoundCustom(double value)
        {
            return (double)RoundCustom((decimal)value);
        }
        
        /// <summary>
        /// تقريب الرقم حسب القاعدة المطلوبة (نسخة float)
        /// </summary>
        /// <param name="value">القيمة المراد تقريبها</param>
        /// <returns>القيمة مقربة حسب القاعدة</returns>
        public static float RoundCustom(float value)
        {
            return (float)RoundCustom((decimal)value);
        }
    }
}

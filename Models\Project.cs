using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FinancialTracker.Helpers;

namespace FinancialTracker.Models
{
    public class Project
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string Description { get; set; } = string.Empty;

        [MaxLength(50)]
        public string Status { get; set; } = "Active";

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // PO (Purchase Order) Information
        public DateTime? PODate { get; set; }

        private decimal _poAmount = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal POAmount
        {
            get => MathHelper.RoundCustom(_poAmount);
            set => _poAmount = value;
        }

        // Manual Financial Input
        private decimal _manualTasksAmount = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal ManualTasksAmount
        {
            get => MathHelper.RoundCustom(_manualTasksAmount);
            set => _manualTasksAmount = value;
        }

        private decimal _manualServicesAmount = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal ManualServicesAmount
        {
            get => MathHelper.RoundCustom(_manualServicesAmount);
            set => _manualServicesAmount = value;
        }

        // Navigation properties
        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
        public virtual ICollection<Commitment> Commitments { get; set; } = new List<Commitment>();

        // Computed properties
        [NotMapped]
        public decimal TotalAmount => GetTotalAmount();

        [NotMapped]
        public decimal PaidAmount => GetPaidAmount();

        [NotMapped]
        public decimal RemainingAmount => TotalAmount - PaidAmount;

        // Detailed financial breakdown
        [NotMapped]
        public decimal TasksAmount => ManualTasksAmount > 0 ? ManualTasksAmount : GetTasksAmount();

        [NotMapped]
        public decimal ServicesAmount => ManualServicesAmount > 0 ? ManualServicesAmount : GetServicesAmount();

        [NotMapped]
        public decimal SpentAmount => PaidAmount;

        [NotMapped]
        public decimal RemainingFromPO => POAmount - PaidAmount;

        private decimal GetTotalAmount()
        {
            decimal total = 0;
            foreach (var invoice in Invoices)
            {
                total += invoice.AmountUSD;
            }
            return MathHelper.RoundCustom(total);
        }

        private decimal GetPaidAmount()
        {
            decimal total = 0;
            foreach (var invoice in Invoices)
            {
                total += invoice.PaidAmount;
            }
            return MathHelper.RoundCustom(total);
        }

        private decimal GetTasksAmount()
        {
            decimal total = 0;
            foreach (var commitment in Commitments)
            {
                if (commitment.Type.Contains("software") ||
                    commitment.Type.Contains("hardware") ||
                    commitment.Type.ToLower().Contains("software") ||
                    commitment.Type.ToLower().Contains("hardware") ||
                    commitment.Type.ToLower().Contains("task"))
                {
                    total += commitment.AmountUSD;
                }
            }
            return MathHelper.RoundCustom(total);
        }

        private decimal GetServicesAmount()
        {
            decimal total = 0;
            foreach (var commitment in Commitments)
            {
                if (commitment.Type.Contains("services") ||
                    commitment.Type.ToLower().Contains("service"))
                {
                    total += commitment.AmountUSD;
                }
            }
            return MathHelper.RoundCustom(total);
        }
    }
}

#nullable enable
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;

namespace FinancialTracker.Views
{
    public partial class InvoiceSelectionDialog : Window
    {
        public Invoice? SelectedInvoice { get; private set; }

        public InvoiceSelectionDialog(IEnumerable<Invoice> invoices)
        {
            InitializeComponent();
            InvoicesDataGrid.ItemsSource = invoices.ToList();
        }

        private void InvoicesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                SelectedInvoice = selectedInvoice;
                SelectButton.IsEnabled = true;
                SelectionInfoText.Text = $"Selected: Invoice #{selectedInvoice.InvoiceNumber} - {selectedInvoice.Type}";
            }
            else
            {
                SelectedInvoice = null;
                SelectButton.IsEnabled = false;
                SelectionInfoText.Text = "Please select an invoice from the list above.";
            }
        }

        private void SelectButton_Click(object sender, RoutedEventArgs e)
        {
            if (SelectedInvoice != null)
            {
                DialogResult = true;
                Close();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}

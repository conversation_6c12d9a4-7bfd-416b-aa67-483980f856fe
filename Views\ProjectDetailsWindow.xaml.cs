#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;
using FinancialTracker.Views;

namespace FinancialTracker
{
    public partial class ProjectDetailsWindow : Window
    {
        private Project? _project;
        private int _projectId;

        public ProjectDetailsWindow(int projectId)
        {
            InitializeComponent();
            _projectId = projectId;
            LoadProjectData();
        }

        private async void LoadProjectData()
        {
            try
            {
                StatusText.Text = "Loading project data...";
                
                _project = await App.DataService.GetProjectByIdAsync(_projectId);
                if (_project == null)
                {
                    MessageBox.Show("Project not found.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    Close();
                    return;
                }

                // Update header and project info
                ProjectNameText.Text = $"Project: {_project.Name}";
                Title = $"Project Details - {_project.Name}";

                ProjectNameDetail.Text = _project.Name;
                ProjectDescriptionDetail.Text = string.IsNullOrEmpty(_project.Description) ? "No description" : _project.Description;
                ProjectCreatedDetail.Text = _project.CreatedDate.ToString("yyyy-MM-dd");
                ProjectStatusDetail.Text = _project.Status;

                // Update PO information
                PODateDetail.Text = _project.PODate?.ToString("yyyy-MM-dd") ?? "Not specified";
                POAmountDetail.Text = $"${_project.POAmount:N2}";
                SpentFromPODetail.Text = $"${_project.SpentAmount:N2}";
                RemainingFromPODetail.Text = $"${_project.RemainingFromPO:N2}";

                // Load invoices and commitments
                await LoadInvoicesData();
                await LoadCommitmentsData();
                UpdateStatistics();

                StatusText.Text = "Ready";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading project data: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "Error loading data";
            }
        }

        private async System.Threading.Tasks.Task LoadInvoicesData()
        {
            try
            {
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var projectInvoices = allInvoices.Where(i => i.ProjectId == _projectId).ToList();
                InvoicesDataGrid.ItemsSource = projectInvoices;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading invoices: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadCommitmentsData()
        {
            try
            {
                var allCommitments = await App.DataService.GetCommitmentsAsync();
                var projectCommitments = allCommitments.Where(c => c.ProjectId == _projectId).ToList();
                
                // Add invoice count for each commitment
                var allInvoices = await App.DataService.GetInvoicesAsync();
                foreach (var commitment in projectCommitments)
                {
                    var relatedInvoices = allInvoices.Where(i => i.CommitmentId == commitment.Id).Count();
                    // We'll add this as a property later
                }
                
                CommitmentsDataGrid.ItemsSource = projectCommitments;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading commitments: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private async void UpdateStatistics()
        {
            try
            {
                // Load all data
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var projectInvoices = allInvoices.Where(i => i.ProjectId == _projectId).ToList();

                var allCommitments = await App.DataService.GetCommitmentsAsync();
                var projectCommitments = allCommitments.Where(c => c.ProjectId == _projectId).ToList();

                // Calculate category amounts - use manual values if available, otherwise calculate from commitments
                var tasksAmount = _project?.ManualTasksAmount > 0 ? _project.ManualTasksAmount :
                    projectCommitments.Where(c =>
                        c.Type.Contains("software") ||
                        c.Type.Contains("hardware") ||
                        c.Type.ToLower().Contains("software") ||
                        c.Type.ToLower().Contains("hardware") ||
                        c.Type.ToLower().Contains("task")).Sum(c => c.AmountUSD);

                var servicesAmount = _project?.ManualServicesAmount > 0 ? _project.ManualServicesAmount :
                    projectCommitments.Where(c =>
                        c.Type.Contains("services") ||
                        c.Type.ToLower().Contains("service")).Sum(c => c.AmountUSD);

                // Calculate spent amounts by category
                var taskInvoices = projectInvoices.Where(i =>
                    projectCommitments.Any(c => c.Id == i.CommitmentId && (
                        c.Type.Contains("software") ||
                        c.Type.Contains("hardware") ||
                        c.Type.ToLower().Contains("software") ||
                        c.Type.ToLower().Contains("hardware") ||
                        c.Type.ToLower().Contains("task")))).ToList();

                var serviceInvoices = projectInvoices.Where(i =>
                    projectCommitments.Any(c => c.Id == i.CommitmentId && (
                        c.Type.Contains("services") ||
                        c.Type.ToLower().Contains("service")))).ToList();

                var tasksSpent = taskInvoices.Sum(i => i.PaidAmount);
                var servicesSpent = serviceInvoices.Sum(i => i.PaidAmount);
                var totalSpent = projectInvoices.Sum(i => i.PaidAmount);

                // Calculate remaining amounts
                var tasksRemaining = tasksAmount - tasksSpent;
                var servicesRemaining = servicesAmount - servicesSpent;
                var poRemaining = (_project?.POAmount ?? 0) - totalSpent;

                // Update PO Summary
                POTotalValueText.Text = $"${_project?.POAmount ?? 0:N0}";
                POSpentText.Text = $"${totalSpent:N0}";
                PORemainingText.Text = $"${poRemaining:N0}";

                // Update Tasks Summary
                TasksTotalValueText.Text = $"${tasksAmount:N0}";
                TasksSpentText.Text = $"${tasksSpent:N0}";
                TasksRemainingText.Text = $"${tasksRemaining:N0}";

                // Update Services Summary
                ServicesTotalValueText.Text = $"${servicesAmount:N0}";
                ServicesSpentText.Text = $"${servicesSpent:N0}";
                ServicesRemainingText.Text = $"${servicesRemaining:N0}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error updating statistics: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Project management
        private void EditProjectButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ProjectDialog(_project);
            if (dialog.ShowDialog() == true)
            {
                LoadProjectData(); // Refresh data
            }
        }

        // Invoice management
        private async void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new InvoiceDialog(null, _projectId, null);
            if (dialog.ShowDialog() == true)
            {
                await LoadInvoicesData();
                UpdateStatistics();
            }
        }

        private async void EditInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice invoice)
            {
                var dialog = new InvoiceDialog(invoice);
                if (dialog.ShowDialog() == true)
                {
                    await LoadInvoicesData();
                    UpdateStatistics();
                }
            }
        }

        private async void CopyInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice originalInvoice)
            {
                // Create a new invoice with the same properties
                var newInvoice = new Invoice
                {
                    InvoiceNumber = $"{originalInvoice.InvoiceNumber}_Copy_{DateTime.Now:yyyyMMddHHmmss}",
                    ProjectId = originalInvoice.ProjectId,
                    CommitmentId = originalInvoice.CommitmentId,
                    AmountUSD = originalInvoice.AmountUSD,
                    PaidAmount = 0, // Reset paid amount for new invoice
                    Description = originalInvoice.Description,
                    Type = originalInvoice.Type,
                    InvoiceDate = DateTime.Now,
                    CreatedDate = DateTime.Now
                };

                var dialog = new InvoiceDialog(newInvoice);
                if (dialog.ShowDialog() == true)
                {
                    await LoadInvoicesData();
                    UpdateStatistics();
                }
            }
        }

        private async void OpenInvoiceFileButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice invoice && !string.IsNullOrEmpty(invoice.AttachedFilePath))
            {
                try
                {
                    await App.FileService.OpenFileAsync(invoice.AttachedFilePath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error opening file: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void OpenInvoiceLetterButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice invoice && !string.IsNullOrEmpty(invoice.LetterFilePath))
            {
                try
                {
                    await App.FileService.OpenFileAsync(invoice.LetterFilePath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error opening letter: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void DeleteInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice invoice)
            {
                var result = MessageBox.Show($"Are you sure you want to delete invoice '{invoice.InvoiceNumber}'?", 
                    "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await App.DataService.DeleteInvoiceAsync(invoice.Id);
                        await LoadInvoicesData();
                        UpdateStatistics();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error deleting invoice: {ex.Message}", "Error", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        // Commitment management
        private async void AddCommitmentButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new CommitmentDialog(null, _projectId);
            if (dialog.ShowDialog() == true)
            {
                await LoadCommitmentsData();
                UpdateStatistics();
            }
        }

        private async void EditCommitmentButton_Click(object sender, RoutedEventArgs e)
        {
            if (CommitmentsDataGrid.SelectedItem is Commitment commitment)
            {
                var dialog = new CommitmentDialog(commitment);
                if (dialog.ShowDialog() == true)
                {
                    await LoadCommitmentsData();
                    UpdateStatistics();
                }
            }
        }

        private async void CopyCommitmentButton_Click(object sender, RoutedEventArgs e)
        {
            if (CommitmentsDataGrid.SelectedItem is Commitment originalCommitment)
            {
                // Create a new commitment with the same properties
                var newCommitment = new Commitment
                {
                    Title = $"{originalCommitment.Title}_Copy_{DateTime.Now:yyyyMMddHHmmss}",
                    Type = originalCommitment.Type,
                    ProjectId = originalCommitment.ProjectId,
                    AmountUSD = originalCommitment.AmountUSD,
                    ExchangeRate = originalCommitment.ExchangeRate,
                    Description = originalCommitment.Description,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                var dialog = new CommitmentDialog(newCommitment, _projectId);
                if (dialog.ShowDialog() == true)
                {
                    await LoadCommitmentsData();
                    UpdateStatistics();
                }
            }
        }

        private void ViewCommitmentInvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            if (CommitmentsDataGrid.SelectedItem is Commitment commitment)
            {
                var invoicesWindow = new CommitmentInvoicesWindow(commitment.Id);
                invoicesWindow.Show();
            }
        }

        private async void OpenCommitmentFileButton_Click(object sender, RoutedEventArgs e)
        {
            if (CommitmentsDataGrid.SelectedItem is Commitment commitment && !string.IsNullOrEmpty(commitment.AttachedFilePath))
            {
                try
                {
                    await App.FileService.OpenFileAsync(commitment.AttachedFilePath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error opening file: {ex.Message}", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void DeleteCommitmentButton_Click(object sender, RoutedEventArgs e)
        {
            if (CommitmentsDataGrid.SelectedItem is Commitment commitment)
            {
                var result = MessageBox.Show($"Are you sure you want to delete commitment '{commitment.Title}'?", 
                    "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await App.DataService.DeleteCommitmentAsync(commitment.Id);
                        await LoadCommitmentsData();
                        UpdateStatistics();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error deleting commitment: {ex.Message}", "Error", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        // Filter functionality
        private async void InvoiceSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            await FilterInvoices();
        }

        private async void ClearInvoiceFilterButton_Click(object sender, RoutedEventArgs e)
        {
            InvoiceSearchTextBox.Text = "";
            await FilterInvoices();
        }

        private async System.Threading.Tasks.Task FilterInvoices()
        {
            try
            {
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var projectInvoices = allInvoices.Where(i => i.ProjectId == _projectId);

                var searchText = InvoiceSearchTextBox.Text?.Trim().ToLower();
                if (!string.IsNullOrEmpty(searchText))
                {
                    projectInvoices = projectInvoices.Where(i =>
                        i.InvoiceNumber.ToLower().Contains(searchText) ||
                        i.Description.ToLower().Contains(searchText));
                }

                InvoicesDataGrid.ItemsSource = projectInvoices.ToList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error filtering invoices: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void CommitmentSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            await FilterCommitments();
        }

        private async void ClearCommitmentFilterButton_Click(object sender, RoutedEventArgs e)
        {
            CommitmentSearchTextBox.Text = "";
            await FilterCommitments();
        }

        private async System.Threading.Tasks.Task FilterCommitments()
        {
            try
            {
                var allCommitments = await App.DataService.GetCommitmentsAsync();
                var projectCommitments = allCommitments.Where(c => c.ProjectId == _projectId);

                var searchText = CommitmentSearchTextBox.Text?.Trim().ToLower();
                if (!string.IsNullOrEmpty(searchText))
                {
                    projectCommitments = projectCommitments.Where(c =>
                        c.Title.ToLower().Contains(searchText) ||
                        c.Description.ToLower().Contains(searchText));
                }

                CommitmentsDataGrid.ItemsSource = projectCommitments.ToList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error filtering commitments: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadProjectData(); // Refresh all project data
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }


    }
}

﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FinancialTracker.Migrations
{
    public partial class RemoveSiteManagement : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Commitments_Sites_SiteId",
                table: "Commitments");

            migrationBuilder.DropForeignKey(
                name: "FK_Invoices_Sites_SiteId",
                table: "Invoices");

            migrationBuilder.DropTable(
                name: "ProjectSites");

            migrationBuilder.DropTable(
                name: "Sites");

            migrationBuilder.DropIndex(
                name: "IX_Invoices_SiteId",
                table: "Invoices");

            migrationBuilder.DropIndex(
                name: "IX_Commitments_SiteId",
                table: "Commitments");

            migrationBuilder.DropColumn(
                name: "SiteId",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "SiteId",
                table: "Commitments");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "SiteId",
                table: "Invoices",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SiteId",
                table: "Commitments",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "Sites",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Address = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    ContactEmail = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    ContactPhone = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    DisplayName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Status = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true, defaultValue: "Active")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Sites", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ProjectSites",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ProjectId = table.Column<int>(type: "INTEGER", nullable: false),
                    SiteId = table.Column<int>(type: "INTEGER", nullable: false),
                    ActualCost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    AssignedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    EndDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    EstimatedBudget = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    StartDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Status = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true, defaultValue: "Active")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProjectSites", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProjectSites_Projects_ProjectId",
                        column: x => x.ProjectId,
                        principalTable: "Projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ProjectSites_Sites_SiteId",
                        column: x => x.SiteId,
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "Sites",
                columns: new[] { "Id", "Address", "ContactEmail", "ContactPhone", "CreatedDate", "Description", "DisplayName", "Name", "Status" },
                values: new object[] { 1, null, null, null, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "الموقع الرئيسي الأول", "الموقع الأول - NN1", "nn1", "Active" });

            migrationBuilder.InsertData(
                table: "Sites",
                columns: new[] { "Id", "Address", "ContactEmail", "ContactPhone", "CreatedDate", "Description", "DisplayName", "Name", "Status" },
                values: new object[] { 2, null, null, null, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "الموقع الرئيسي الثاني", "الموقع الثاني - NN2", "nn2", "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 1, null, new DateTime(2024, 1, 6, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 1, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 2, null, new DateTime(2024, 1, 6, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 1, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 3, null, new DateTime(2024, 1, 11, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 2, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 4, null, new DateTime(2024, 1, 11, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 2, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 5, null, new DateTime(2024, 1, 16, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 3, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 6, null, new DateTime(2024, 1, 16, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 3, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 7, null, new DateTime(2024, 1, 21, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 4, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 8, null, new DateTime(2024, 1, 21, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 4, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 9, null, new DateTime(2024, 1, 26, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 5, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 10, null, new DateTime(2024, 1, 26, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 5, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 11, null, new DateTime(2024, 1, 31, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 6, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 12, null, new DateTime(2024, 1, 31, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 6, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 13, null, new DateTime(2024, 2, 5, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 7, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 14, null, new DateTime(2024, 2, 5, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 7, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 15, null, new DateTime(2024, 2, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 8, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 16, null, new DateTime(2024, 2, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 8, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 17, null, new DateTime(2024, 2, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 9, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 18, null, new DateTime(2024, 2, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 9, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 19, null, new DateTime(2024, 2, 20, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 10, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 20, null, new DateTime(2024, 2, 20, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 10, 2, null, "Active" });

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_SiteId",
                table: "Invoices",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_Commitments_SiteId",
                table: "Commitments",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectSites_ProjectId_SiteId",
                table: "ProjectSites",
                columns: new[] { "ProjectId", "SiteId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ProjectSites_SiteId",
                table: "ProjectSites",
                column: "SiteId");

            migrationBuilder.AddForeignKey(
                name: "FK_Commitments_Sites_SiteId",
                table: "Commitments",
                column: "SiteId",
                principalTable: "Sites",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Invoices_Sites_SiteId",
                table: "Invoices",
                column: "SiteId",
                principalTable: "Sites",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}

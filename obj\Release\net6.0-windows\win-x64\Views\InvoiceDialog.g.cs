﻿#pragma checksum "..\..\..\..\..\Views\InvoiceDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "56A41B9881EA11D56D54E90B929B4A700BF5CEEC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FinancialTracker {
    
    
    /// <summary>
    /// InvoiceDialog
    /// </summary>
    public partial class InvoiceDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 24 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoiceNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProjectComboBox;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CommitmentComboBox;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CustomTypeTextBox;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExchangeRateTextBox;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PaidAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker InvoiceDatePicker;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker SignatureDatePicker;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AttachedFileTextBox;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectFileButton;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LetterFileTextBox;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectLetterButton;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\..\Views\InvoiceDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewLetterButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FinancialTracker;component/views/invoicedialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\InvoiceDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.InvoiceNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.ProjectComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 36 "..\..\..\..\..\Views\InvoiceDialog.xaml"
            this.ProjectComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ProjectComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CommitmentComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.TypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 53 "..\..\..\..\..\Views\InvoiceDialog.xaml"
            this.TypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TypeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CustomTypeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.AmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.ExchangeRateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.PaidAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.InvoiceDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 10:
            this.SignatureDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 11:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.AttachedFileTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.SelectFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 125 "..\..\..\..\..\Views\InvoiceDialog.xaml"
            this.SelectFileButton.Click += new System.Windows.RoutedEventHandler(this.SelectFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.LetterFileTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.SelectLetterButton = ((System.Windows.Controls.Button)(target));
            
            #line 153 "..\..\..\..\..\Views\InvoiceDialog.xaml"
            this.SelectLetterButton.Click += new System.Windows.RoutedEventHandler(this.SelectLetterButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ViewLetterButton = ((System.Windows.Controls.Button)(target));
            
            #line 160 "..\..\..\..\..\Views\InvoiceDialog.xaml"
            this.ViewLetterButton.Click += new System.Windows.RoutedEventHandler(this.ViewLetterButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 174 "..\..\..\..\..\Views\InvoiceDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 178 "..\..\..\..\..\Views\InvoiceDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}


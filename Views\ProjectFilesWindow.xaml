<Window x:Class="FinancialTracker.Views.ProjectFilesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Project Files Management"
        Height="700"
        Width="1100"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="FolderOpen" Width="24" Height="24" Margin="0,0,8,0" Foreground="White"/>
                    <TextBlock x:Name="ProjectNameText" Text="Project Files" FontSize="18" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="RefreshButton" Style="{StaticResource MaterialDesignIconButton}"
                            ToolTip="Refresh Files" Margin="4,0" Click="RefreshButton_Click">
                        <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20" Foreground="White"/>
                    </Button>
                    <Button x:Name="AddLetterButton" Content="Add Letter" Style="{StaticResource MaterialDesignFlatButton}"
                            Foreground="White" Margin="8,0" Click="AddLetterButton_Click"/>
                    <Button x:Name="AddFileButton" Content="Add File" Style="{StaticResource MaterialDesignFlatButton}"
                            Foreground="White" Margin="8,0" Click="AddFileButton_Click"/>
                    <Button Content="Close" Style="{StaticResource MaterialDesignFlatButton}"
                            Foreground="White" Margin="8,0" Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" Margin="16">
            <StackPanel>
                <!-- Summary Cards -->
                <Grid Margin="0,0,0,16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- First Row - File Counts -->
                    <Grid Grid.Row="0" Margin="0,0,0,4">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:Card Grid.Column="0" Margin="4" Padding="12" MinHeight="60" Background="#E3F2FD">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="FileMultiple" Width="20" Height="20" Foreground="#1976D2" Margin="0,0,0,4"/>
                                <TextBlock Text="Total Files" Opacity="0.8" FontSize="11" HorizontalAlignment="Center" TextAlignment="Center"/>
                                <TextBlock x:Name="TotalFilesText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#1976D2" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="1" Margin="4" Padding="12" Background="#FFF3E0" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="FileDocument" Width="20" Height="20" Foreground="#F57C00" Margin="0,0,0,4"/>
                                <TextBlock Text="Commitment Files" Opacity="0.8" FontSize="11" HorizontalAlignment="Center" TextAlignment="Center"/>
                                <TextBlock x:Name="CommitmentFilesText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#F57C00" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="2" Margin="4" Padding="12" Background="#E8F5E8" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="Receipt" Width="20" Height="20" Foreground="#388E3C" Margin="0,0,0,4"/>
                                <TextBlock Text="Invoice Files" Opacity="0.8" FontSize="11" HorizontalAlignment="Center" TextAlignment="Center"/>
                                <TextBlock x:Name="InvoiceFilesText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#388E3C" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="3" Margin="4" Padding="12" Background="#F3E5F5" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="Email" Width="20" Height="20" Foreground="#7B1FA2" Margin="0,0,0,4"/>
                                <TextBlock Text="Letter Files" Opacity="0.8" FontSize="11" HorizontalAlignment="Center" TextAlignment="Center"/>
                                <TextBlock x:Name="LetterFilesText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#7B1FA2" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>

                    <!-- Second Row - Size Info -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:Card Grid.Column="0" Margin="4" Padding="12" MinHeight="50" Background="#FFF8E1">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="HardDisk" Width="18" Height="18" Foreground="#FF8F00" Margin="0,0,0,4"/>
                                <TextBlock Text="Total Files Size" Opacity="0.8" FontSize="11" HorizontalAlignment="Center" TextAlignment="Center"/>
                                <TextBlock x:Name="TotalSizeText" Text="0 MB" FontSize="16" FontWeight="Bold" Foreground="#FF8F00" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>
                </Grid>

                <!-- Files List -->
                <materialDesign:Card Padding="16">
                    <StackPanel>
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="FileMultiple" Width="20" Height="20"
                                                        Foreground="#1976D2" Margin="0,0,8,0"/>
                                <TextBlock Text="Project Files" FontSize="18" FontWeight="SemiBold"
                                          Foreground="#212529" VerticalAlignment="Center"/>
                            </StackPanel>



                            <ComboBox x:Name="FileTypeFilterComboBox" Grid.Column="1" Width="160"
                                      materialDesign:HintAssist.Hint="Filter by Type"
                                      SelectionChanged="FileTypeFilterComboBox_SelectionChanged">
                                <ComboBoxItem Content="All Files" IsSelected="True"/>
                                <ComboBoxItem Content="Invoice Files"/>
                                <ComboBoxItem Content="Commitment Files"/>
                                <ComboBoxItem Content="Letter Files"/>
                            </ComboBox>
                        </Grid>

                        <!-- Search Box -->
                        <materialDesign:Card Padding="12" Margin="0,0,0,16" Background="#F8F9FA">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <materialDesign:PackIcon Grid.Column="0" Kind="Magnify" Width="20" Height="20"
                                                        Foreground="#6C757D" VerticalAlignment="Center" Margin="0,0,8,0"/>

                                <TextBox x:Name="FileSearchTextBox" Grid.Column="1"
                                        materialDesign:HintAssist.Hint="Search files by name or source..."
                                        BorderThickness="0" Background="Transparent"
                                        FontSize="13" TextChanged="FileSearchTextBox_TextChanged"/>
                            </Grid>
                        </materialDesign:Card>

                        <DataGrid x:Name="FilesDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                  IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal"
                                  MaxHeight="400" HorizontalScrollBarVisibility="Auto"
                                  Background="White" RowBackground="White" AlternatingRowBackground="#FAFBFC"
                                  BorderBrush="#E1E5E9" BorderThickness="1" FontSize="13" FontFamily="Segoe UI">

                            <DataGrid.ColumnHeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#F8F9FA"/>
                                    <Setter Property="Foreground" Value="#495057"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="Padding" Value="12,10"/>
                                    <Setter Property="BorderBrush" Value="#DEE2E6"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Left"/>
                                </Style>
                            </DataGrid.ColumnHeaderStyle>

                            <DataGrid.RowStyle>
                                <Style TargetType="DataGridRow">
                                    <Setter Property="Height" Value="50"/>
                                    <Setter Property="BorderBrush" Value="#F1F3F4"/>
                                    <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#F8F9FA"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.RowStyle>

                            <DataGrid.CellStyle>
                                <Style TargetType="DataGridCell">
                                    <Setter Property="BorderThickness" Value="0"/>
                                    <Setter Property="Padding" Value="12,8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="DataGridCell">
                                                <Border Background="{TemplateBinding Background}"
                                                       BorderBrush="{TemplateBinding BorderBrush}"
                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                       Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </DataGrid.CellStyle>
                            <DataGrid.Columns>
                                <DataGridTemplateColumn Header="Type" Width="*">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <materialDesign:PackIcon Kind="{Binding FileTypeIcon}" Width="18" Height="18"
                                                                       Foreground="{Binding FileTypeColor}" Margin="0,0,6,0"/>
                                                <TextBlock Text="{Binding FileType}" FontSize="11" FontWeight="Medium" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <DataGridTemplateColumn Header="File Name" Width="2*">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding FileName}" FontWeight="Medium" FontSize="12"
                                                      TextTrimming="CharacterEllipsis" ToolTip="{Binding FileName}"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <DataGridTemplateColumn Header="Source" Width="*">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Source}" FontSize="12" Foreground="#6C757D"
                                                      TextTrimming="CharacterEllipsis" ToolTip="{Binding Source}"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <DataGridTemplateColumn Header="Size" Width="*">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding FileSizeFormatted}" FontSize="11"
                                                      HorizontalAlignment="Center" Foreground="#6C757D"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <DataGridTemplateColumn Header="Date Added" Width="*">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding DateAdded, StringFormat='{}{0:MM/dd/yyyy}'}"
                                                      FontSize="11" Foreground="#6C757D" HorizontalAlignment="Center"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTemplateColumn Header="Actions" Width="140">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                        Width="32" Height="32" Margin="2"
                                                        ToolTip="Open File" Click="OpenFileButton_Click"
                                                        Background="#E3F2FD" Foreground="#1976D2">
                                                    <materialDesign:PackIcon Kind="OpenInNew" Width="16" Height="16"/>
                                                </Button>

                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                        Width="32" Height="32" Margin="2"
                                                        ToolTip="Copy Path" Click="CopyPathButton_Click"
                                                        Background="#F3E5F5" Foreground="#7B1FA2">
                                                    <materialDesign:PackIcon Kind="ContentCopy" Width="16" Height="16"/>
                                                </Button>

                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                        Width="32" Height="32" Margin="2"
                                                        ToolTip="Delete File" Click="DeleteFileButton_Click"
                                                        Background="#FFEBEE" Foreground="#D32F2F">
                                                    <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                                </Button>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- Status Bar -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="16,8">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Information" Width="16" Height="16" Margin="0,0,8,0"/>
                    <TextBlock x:Name="StatusText" Text="Ready" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</Window>

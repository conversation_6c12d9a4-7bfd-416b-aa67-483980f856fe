# Financial Tracker - Desktop Application

A comprehensive offline financial tracking system for managing projects, invoices, commitments, and multi-site operations.

## Features
- **Multi-Site Management** - Support for multiple deployment sites (nn1, nn2, etc.)
- **Projects Management** - Complete project lifecycle management
- **Invoices & Commitments** - USD amounts with file attachments
- **Advanced Reporting** - Site-specific and cross-site comparison reports
- **Modern UI** - Material Design interface with enhanced user experience
- **File Management** - Integrated file attachment and organization system
- **Dashboard** - Real-time statistics and project summaries
- **Enhanced Letters System** - NEW! Improved letter viewing with image display and detailed information
- **Fully Portable** - Self-contained offline application

## New Letter System Features ✨
- **Letter Image Display** - View uploaded letter images directly in the interface
- **Detailed Commitment Information** - Shows commitment title, date, and amount in organized cards
- **Individual Invoice Display** - Each selected invoice is displayed separately with its number, date, and amount
- **Improved Layout** - Better organized sections with color-coded information cards
- **File Management** - Easy access to all related files (letter, commitment, and invoice files)

## Building the Application

### Quick Build
Run `build.bat` in the project folder for automated build and publish

### Development Run
Run `run-dev.bat` for development testing

### Manual Build
```bash
dotnet restore
dotnet build --configuration Release
dotnet publish --configuration Release --self-contained true --runtime win-x64 --output ./publish --property:PublishSingleFile=true
```

## Running
1. Navigate to the `publish` folder after building
2. Run `FinancialTracker.exe`
3. The app will create necessary folders and database automatically

## Project Structure
```
FinancialTracker/
├── Data/                    # Database context and migrations
├── Models/                  # Data models and entities
├── Services/               # Business logic and data services
├── Views/                  # UI windows and dialogs
├── Helpers/                # Utility classes
├── data/                   # Runtime data folder
│   ├── invoices/          # Invoice attachments
│   ├── commitments/       # Commitment attachments
│   └── replies/           # Reply attachments
├── FinancialTracker.db    # SQLite database
├── settings.json          # Application settings
└── FinancialTracker.exe   # Main executable
```

## How to Use the Enhanced Letter System

### Creating a Letter
1. Navigate to the Letters section in your project
2. Click "Add New Letter"
3. Upload your letter file (images will be displayed in the viewer)
4. Select a commitment from the dropdown (optional)
5. Choose related invoices using the "Select Invoices" button
6. Add any notes and save

### Viewing a Letter
When you open a letter, you'll see:
- **Letter Image Section** - The uploaded letter image (if it's an image file)
- **Basic Information** - Letter title and send date in colored cards
- **Commitment Details** - If a commitment is selected, shows title, date, and amount
- **Individual Invoices** - Each selected invoice displayed separately with:
  - Invoice number
  - Invoice date
  - Invoice amount
  - Open file button (if file exists)
- **All Related Files** - Complete list of all associated files

### File Management
- All files are automatically organized in the project folder structure
- Click "Open File" buttons to view individual documents
- Files are validated for existence and marked accordingly

## Requirements
- Windows 10/11
- No .NET runtime required (self-contained)
- Minimum 4GB RAM recommended
- 100MB free disk space

<Window x:Class="FinancialTracker.Views.InvoiceSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Select Invoice for Letter"
        Height="500"
        Width="700"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,12">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="Email" Width="24" Height="24" Margin="0,0,12,0" Foreground="White"/>
                <TextBlock Text="Select Invoice for Letter Upload" FontSize="18" VerticalAlignment="Center" Foreground="White"/>
            </StackPanel>
        </materialDesign:ColorZone>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" Margin="16">
            <StackPanel>
                <TextBlock Text="Select an unsigned invoice to attach the letter:" 
                          FontSize="14" Margin="0,0,0,16" Opacity="0.8"/>
                
                <DataGrid x:Name="InvoicesDataGrid" AutoGenerateColumns="False" CanUserAddRows="False" 
                          IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal"
                          MaxHeight="300" SelectionChanged="InvoicesDataGrid_SelectionChanged">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Invoice #" Binding="{Binding InvoiceNumber}" Width="100"/>
                        <DataGridTextColumn Header="Type" Binding="{Binding Type}" Width="120"/>
                        <DataGridTextColumn Header="Amount (EGP)" Binding="{Binding Amount, StringFormat='{}{0:N2}'}" Width="120"/>
                        <DataGridTextColumn Header="Amount (USD)" Binding="{Binding AmountUSD, StringFormat='{}{0:N2}'}" Width="120"/>
                        <DataGridTextColumn Header="Created Date" Binding="{Binding CreatedDate, StringFormat='{}{0:yyyy-MM-dd}'}" Width="120"/>
                        <DataGridTextColumn Header="Status" Binding="{Binding SignatureStatus}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>

                <TextBlock x:Name="SelectionInfoText" Text="Please select an invoice from the list above." 
                          FontSize="12" Margin="0,16,0,0" Opacity="0.6"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="16,12">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button x:Name="SelectButton" Content="Select" Style="{StaticResource MaterialDesignRaisedButton}" 
                        Margin="0,0,8,0" Click="SelectButton_Click" IsEnabled="False"/>
                <Button Content="Cancel" Style="{StaticResource MaterialDesignOutlinedButton}" 
                        Click="CancelButton_Click"/>
            </StackPanel>
        </materialDesign:ColorZone>
    </Grid>
</Window>

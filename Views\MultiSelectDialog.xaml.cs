#nullable enable
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace FinancialTracker.Views
{
    public partial class MultiSelectDialog : Window
    {
        private readonly ObservableCollection<SelectableItem> _items = new();
        private readonly List<SelectableItem> _allItems = new();

        public List<object> SelectedItems => _items.Where(i => i.IsSelected).Select(i => i.OriginalItem).ToList();

        public MultiSelectDialog(string title, IEnumerable<object> items, Func<object, string> displayTextSelector, IEnumerable<object>? selectedItems = null)
        {
            InitializeComponent();
            
            txtTitle.Text = title;
            Title = title;

            var selectedSet = selectedItems?.ToHashSet() ?? new HashSet<object>();

            foreach (var item in items)
            {
                var selectableItem = new SelectableItem
                {
                    OriginalItem = item,
                    DisplayText = displayTextSelector(item),
                    IsSelected = selectedSet.Contains(item)
                };
                
                _allItems.Add(selectableItem);
                _items.Add(selectableItem);
            }

            lstItems.ItemsSource = _items;
            UpdateSelectionInfo();

            // Subscribe to property changes
            foreach (var item in _items)
            {
                item.PropertyChanged += Item_PropertyChanged;
            }
        }

        private void Item_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(SelectableItem.IsSelected))
            {
                UpdateSelectionInfo();
            }
        }

        private void UpdateSelectionInfo()
        {
            var selectedCount = _items.Count(i => i.IsSelected);
            var totalCount = _items.Count;
            
            if (selectedCount == 0)
            {
                txtSelectionInfo.Text = "No items selected";
            }
            else
            {
                txtSelectionInfo.Text = $"Selected {selectedCount} of {totalCount} items";
            }
        }

        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchText = txtSearch.Text.ToLower();
            
            _items.Clear();
            
            var filteredItems = string.IsNullOrWhiteSpace(searchText) 
                ? _allItems 
                : _allItems.Where(i => i.DisplayText.ToLower().Contains(searchText));

            foreach (var item in filteredItems)
            {
                _items.Add(item);
            }

            UpdateSelectionInfo();
        }

        private void BtnSelectAll_Click(object sender, RoutedEventArgs e)
        {
            foreach (var item in _items)
            {
                item.IsSelected = true;
            }
        }

        private void BtnDeselectAll_Click(object sender, RoutedEventArgs e)
        {
            foreach (var item in _items)
            {
                item.IsSelected = false;
            }
        }

        private void BtnOK_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    public class SelectableItem : INotifyPropertyChanged
    {
        private bool _isSelected;

        public object OriginalItem { get; set; } = null!;
        public string DisplayText { get; set; } = string.Empty;

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsSelected)));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
    }

    // Generic version for type safety
    public class MultiSelectDialog<T> : Window where T : class
    {
        private readonly ObservableCollection<SelectableItem<T>> _items = new();
        private readonly List<SelectableItem<T>> _allItems = new();

        public List<T> SelectedItems => _items.Where(i => i.IsSelected).Select(i => i.OriginalItem).ToList();

        public MultiSelectDialog(string title, IEnumerable<T> items, Func<T, string> displayTextSelector, IEnumerable<T>? selectedItems = null)
        {
            
            Title = title;

            var selectedSet = selectedItems?.ToHashSet() ?? new HashSet<T>();

            foreach (var item in items)
            {
                var selectableItem = new SelectableItem<T>
                {
                    OriginalItem = item,
                    DisplayText = displayTextSelector(item),
                    IsSelected = selectedSet.Contains(item)
                };
                
                _allItems.Add(selectableItem);
                _items.Add(selectableItem);
            }

            SetupUI(title);
        }

        private void SetupUI(string title)
        {
            // Create UI programmatically since we can't inherit from the XAML window
            Width = 600;
            Height = 500;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            FlowDirection = FlowDirection.RightToLeft;
            Title = title;

            var grid = new Grid { Margin = new Thickness(16) };
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Header
            var headerBorder = new Border
            {
                Background = System.Windows.Media.Brushes.DarkBlue,
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(16),
                Margin = new Thickness(8)
            };
            
            var headerText = new TextBlock
            {
                Text = title,
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = System.Windows.Media.Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center
            };
            
            headerBorder.Child = headerText;
            Grid.SetRow(headerBorder, 0);
            grid.Children.Add(headerBorder);

            // Content
            var contentBorder = new Border
            {
                Background = System.Windows.Media.Brushes.White,
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(16),
                Margin = new Thickness(8)
            };

            var listBox = new ListBox
            {
                ItemsSource = _items,
                SelectionMode = SelectionMode.Multiple
            };

            var template = new DataTemplate();
            var factory = new FrameworkElementFactory(typeof(CheckBox));
            factory.SetBinding(CheckBox.ContentProperty, new System.Windows.Data.Binding("DisplayText"));
            factory.SetBinding(CheckBox.IsCheckedProperty, new System.Windows.Data.Binding("IsSelected"));
            factory.SetValue(CheckBox.MarginProperty, new Thickness(4));
            template.VisualTree = factory;
            listBox.ItemTemplate = template;

            contentBorder.Child = listBox;
            Grid.SetRow(contentBorder, 1);
            grid.Children.Add(contentBorder);

            // Buttons
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(8)
            };

            var btnOK = new Button
            {
                Content = "OK",
                Width = 100,
                Margin = new Thickness(8, 0, 8, 0),
                Padding = new Thickness(16, 8, 16, 8)
            };
            btnOK.Click += (s, e) => { DialogResult = true; Close(); };

            var btnCancel = new Button
            {
                Content = "Cancel",
                Width = 100,
                Margin = new Thickness(8, 0, 8, 0),
                Padding = new Thickness(16, 8, 16, 8)
            };
            btnCancel.Click += (s, e) => { DialogResult = false; Close(); };

            buttonPanel.Children.Add(btnOK);
            buttonPanel.Children.Add(btnCancel);

            Grid.SetRow(buttonPanel, 2);
            grid.Children.Add(buttonPanel);

            Content = grid;

            // Subscribe to property changes
            foreach (var item in _items)
            {
                item.PropertyChanged += Item_PropertyChanged;
            }
        }

        private void Item_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            // Update selection info if needed
        }
    }

    public class SelectableItem<T> : INotifyPropertyChanged where T : class
    {
        private bool _isSelected;

        public T OriginalItem { get; set; } = null!;
        public string DisplayText { get; set; } = string.Empty;

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsSelected)));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
    }
}

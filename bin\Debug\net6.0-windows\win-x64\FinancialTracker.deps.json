{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {}, ".NETCoreApp,Version=v6.0/win-x64": {"FinancialTracker/1.0.0": {"dependencies": {"MaterialDesignColors": "2.1.4", "MaterialDesignThemes": "4.9.0", "Microsoft.EntityFrameworkCore.Design": "6.0.25", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.25", "Microsoft.EntityFrameworkCore.Tools": "6.0.25", "Microsoft.VisualBasic": "10.3.0", "Newtonsoft.Json": "13.0.3", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "6.0.35", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "6.0.35"}, "runtime": {"FinancialTracker.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/6.0.35": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.100.3524.45918"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "6.0.3524.45918"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.40.33810.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "42.42.42.42424"}, "api-ms-win-core-console-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-console-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-datetime-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-debug-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-errorhandling-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-fibers-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l2-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-handle-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-heap-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-interlocked-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-libraryloader-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-localization-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-memory-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-namedpipe-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processenvironment-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processthreads-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processthreads-l1-1-1.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-profile-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-rtlsupport-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-string-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-synch-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-synch-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-sysinfo-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-timezone-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-util-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-conio-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-convert-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-environment-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-filesystem-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-heap-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-locale-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-math-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-multibyte-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-private-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-process-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-runtime-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-stdio-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-string-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-time-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-utility-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "clretwrc.dll": {"fileVersion": "6.0.3524.45918"}, "clrjit.dll": {"fileVersion": "6.0.3524.45918"}, "coreclr.dll": {"fileVersion": "6.0.3524.45918"}, "createdump.exe": {"fileVersion": "6.0.3524.45918"}, "dbgshim.dll": {"fileVersion": "6.0.3524.45918"}, "hostfxr.dll": {"fileVersion": "6.0.3524.45918"}, "hostpolicy.dll": {"fileVersion": "6.0.3524.45918"}, "mscordaccore.dll": {"fileVersion": "6.0.3524.45918"}, "mscordaccore_amd64_amd64_6.0.3524.45918.dll": {"fileVersion": "6.0.3524.45918"}, "mscordbi.dll": {"fileVersion": "6.0.3524.45918"}, "mscorrc.dll": {"fileVersion": "6.0.3524.45918"}, "msquic.dll": {"fileVersion": "*******"}, "ucrtbase.dll": {"fileVersion": "10.0.22000.194"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/6.0.35": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46209"}, "DirectWriteForwarder.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46209"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3524.46209"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "PresentationCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "PresentationFramework-SystemCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "PresentationFramework-SystemData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "PresentationFramework-SystemDrawing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "PresentationFramework-SystemXml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "PresentationFramework-SystemXmlLinq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "PresentationFramework.Aero.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "PresentationFramework.Aero2.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "PresentationFramework.AeroLite.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "PresentationFramework.Classic.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "PresentationFramework.Luna.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "PresentationFramework.Royale.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "PresentationFramework.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "PresentationUI.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "ReachFramework.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Design.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46209"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Drawing.Design.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46209"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46209"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Printing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Windows.Controls.Ribbon.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.45918"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46209"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46209"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46209"}, "System.Windows.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46209"}, "System.Windows.Input.Manipulations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "System.Windows.Presentation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "System.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "UIAutomationClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "UIAutomationClientSideProviders.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "UIAutomationProvider.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "UIAutomationTypes.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}, "WindowsFormsIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3524.46214"}}, "native": {"D3DCompiler_47_cor3.dll": {"fileVersion": "10.0.22621.3233"}, "PenImc_cor3.dll": {"fileVersion": "6.0.3524.46214"}, "PresentationNative_cor3.dll": {"fileVersion": "6.0.24.36501"}, "vcruntime140_cor3.dll": {"fileVersion": "14.40.33810.0"}, "wpfgfx_cor3.dll": {"fileVersion": "6.0.3524.46214"}}}, "Humanizer.Core/2.8.26": {"runtime": {"lib/netstandard2.0/Humanizer.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.26.1919"}}}, "MaterialDesignColors/2.1.4": {"runtime": {"lib/net6.0/MaterialDesignColors.dll": {"assemblyVersion": "2.1.4.0", "fileVersion": "2.1.4.0"}}}, "MaterialDesignThemes/4.9.0": {"dependencies": {"MaterialDesignColors": "2.1.4", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "runtime": {"lib/net6.0/MaterialDesignThemes.Wpf.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "Microsoft.Data.Sqlite.Core/6.0.25": {"dependencies": {"SQLitePCLRaw.core": "2.1.2"}, "runtime": {"lib/net6.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "6.0.25.0", "fileVersion": "6.0.2523.52312"}}}, "Microsoft.EntityFrameworkCore/6.0.25": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.25", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.25", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.Logging": "6.0.0", "System.Collections.Immutable": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "6.0.25.0", "fileVersion": "6.0.2523.52312"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.25": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "6.0.25.0", "fileVersion": "6.0.2523.52312"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.25": {}, "Microsoft.EntityFrameworkCore.Design/6.0.25": {"dependencies": {"Humanizer.Core": "2.8.26", "Microsoft.EntityFrameworkCore.Relational": "6.0.25"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "6.0.25.0", "fileVersion": "6.0.2523.52312"}}}, "Microsoft.EntityFrameworkCore.Relational/6.0.25": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.25", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "6.0.25.0", "fileVersion": "6.0.2523.52312"}}}, "Microsoft.EntityFrameworkCore.Sqlite/6.0.25": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "6.0.25", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.2"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/6.0.25": {"dependencies": {"Microsoft.Data.Sqlite.Core": "6.0.25", "Microsoft.EntityFrameworkCore.Relational": "6.0.25", "Microsoft.Extensions.DependencyModel": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "6.0.25.0", "fileVersion": "6.0.2523.52312"}}}, "Microsoft.EntityFrameworkCore.Tools/6.0.25": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "6.0.25"}}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1022.47605"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.DependencyModel/6.0.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.VisualBasic/10.3.0": {}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"runtime": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.39.4716"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.2": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.2", "SQLitePCLRaw.provider.e_sqlite3": "2.1.2"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "SQLitePCLRaw.core/2.1.2": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.2": {"native": {"runtimes/win-x64/native/e_sqlite3.dll": {"fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.2": {"dependencies": {"SQLitePCLRaw.core": "2.1.2"}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "System.Buffers/4.5.1": {}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Memory/4.5.4": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}}}}, "libraries": {"FinancialTracker/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/6.0.35": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/6.0.35": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Humanizer.Core/2.8.26": {"type": "package", "serviceable": true, "sha512": "sha512-OiKusGL20vby4uDEswj2IgkdchC1yQ6rwbIkZDVBPIR6al2b7n3pC91elBul9q33KaBgRKhbZH3+2Ur4fnWx2A==", "path": "humanizer.core/2.8.26", "hashPath": "humanizer.core.2.8.26.nupkg.sha512"}, "MaterialDesignColors/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-C4Oy+qkjMoMPoZKyqYdCnIYtK8c0OSIHmNP73Vgc69NjiUG093xTkE7W/Ks54cTDS7fmWOtUHfwISTVTtb/YKg==", "path": "materialdesigncolors/2.1.4", "hashPath": "materialdesigncolors.2.1.4.nupkg.sha512"}, "MaterialDesignThemes/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bp9Auw70j+9V7WsUMT4pc8ulVzfL0Eav/tyGgICDirxxhKJwhqtC/6PRkTUm+R1t9611xiDuk5pSUNdDV6vfOQ==", "path": "materialdesignthemes/4.9.0", "hashPath": "materialdesignthemes.4.9.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-rbXNoMg/ylGyJxLcyetojuXFzvDG85M31DfFbqL8veN4P8oG6wmnPwWNn3/bDIEDVvdw15R092dxpobQeQcjGg==", "path": "microsoft.data.sqlite.core/6.0.25", "hashPath": "microsoft.data.sqlite.core.6.0.25.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-txcqw2xrmvMoTIgzAdUk8JHLELofGgTK3i6glswVZs4SC8BOU1M/iSAtwMIVtAtfzxuBIUAbHPx+Ly6lfkYe7g==", "path": "microsoft.entityframeworkcore/6.0.25", "hashPath": "microsoft.entityframeworkcore.6.0.25.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-DalO25C96LsIfAPlyizyun9y1XrIquRugPEGXC8+z7dFo+GyU0LRd0R11JDd3rJWjR18NOFYwqNenjyDpNRO3A==", "path": "microsoft.entityframeworkcore.abstractions/6.0.25", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.25.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-i6UpdWqWxSBbIFOkaMoubM40yIjTZO+0rIUkY5JRltSeFI4PzncBBQcNVNXXjAmiLXF/xY0xTS+ykClbkV46Yg==", "path": "microsoft.entityframeworkcore.analyzers/6.0.25", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.25.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-YawyMKj1f+GkwHrxMIf9tX84sMGgLFa5YoRmyuUugGhffiubkVLYIrlm4W0uSy2NzX4t6+V7keFLQf7lRQvDmA==", "path": "microsoft.entityframeworkcore.design/6.0.25", "hashPath": "microsoft.entityframeworkcore.design.6.0.25.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-ci2lR++x7R7LR71+HoeRnB9Z5VeOQ1ILLbFRhsjjWZyLrAMkdq7TK9Ll47jo1TXDWF8Ddeap1JgcptgPKkWSRA==", "path": "microsoft.entityframeworkcore.relational/6.0.25", "hashPath": "microsoft.entityframeworkcore.relational.6.0.25.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-vaQNuXgUN0nIzFXQiPSb9iAaJqLvZA164Sx9mjF5rFQS5cwQ/AiymF0e4J0QH3P07Mf3zEVZE5u2fTO0NacuMQ==", "path": "microsoft.entityframeworkcore.sqlite/6.0.25", "hashPath": "microsoft.entityframeworkcore.sqlite.6.0.25.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-IU4E8I9FS2sUVxJJ0w/4jogLQ8C0zvu/SO6b1tRmiiCtTrHhjUB0tqhxjrFnDXZ/mpCJOElw50+qhbcElm0CYw==", "path": "microsoft.entityframeworkcore.sqlite.core/6.0.25", "hashPath": "microsoft.entityframeworkcore.sqlite.core.6.0.25.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-2iPMR+DHXh2Xn9qoJ0ejzdHblpns73e1pZ/pyRbYDQi0HPJyq1/pTYDda1owJ5W2lxAGDg8l5Fl1jVp97fTR1g==", "path": "microsoft.entityframeworkcore.tools/6.0.25", "hashPath": "microsoft.entityframeworkcore.tools.6.0.25.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "path": "microsoft.extensions.caching.abstractions/6.0.0", "hashPath": "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "path": "microsoft.extensions.caching.memory/6.0.1", "hashPath": "microsoft.extensions.caching.memory.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vWXPg3HJQIpZkENn1KWq8SfbqVujVD7S7vIAyFXXqK5xkf1Vho+vG0bLBCHxU36lD1cLLtmGpfYf0B3MYFi9tQ==", "path": "microsoft.extensions.dependencyinjection/6.0.1", "hashPath": "microsoft.extensions.dependencyinjection.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TD5QHg98m3+QhgEV1YVoNMl5KtBw/4rjfxLHO0e/YV9bPUBDKntApP4xdrVtGgCeQZHVfC2EXIGsdpRNrr87Pg==", "path": "microsoft.extensions.dependencymodel/6.0.0", "hashPath": "microsoft.extensions.dependencymodel.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "Microsoft.VisualBasic/10.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AvMDjmJHjz9bdlvxiSdEHHcWP+sZtp7zwule5ab6DaUbgoBnwCsd7nymj69vSz18ypXuEv3SI7ZUNwbIKrvtMA==", "path": "microsoft.visualbasic/10.3.0", "hashPath": "microsoft.visualbasic.10.3.0.nupkg.sha512"}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "serviceable": true, "sha512": "sha512-8PZKqw9QOcu42xk8puY4P1+EXHL9YGOR9b7qhaYx5cILHul456H073tj99vyPcCt0W0781T9RwHqkx507ZyUpQ==", "path": "microsoft.xaml.behaviors.wpf/1.1.39", "hashPath": "microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-ilkvNhrTersLmIVAcDwwPqfhUFCg19Z1GVMvCSi3xk6Akq94f4qadLORQCq/T8+9JgMiPs+F/NECw5uauviaNw==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-A8EBepVqY2lnAp3a8jnhbgzF2tlj2S3HcJQGANTYg/TbYbKa8Z5cM1h74An/vy0svhfzT7tVY0sFmUglLgv+2g==", "path": "sqlitepclraw.core/2.1.2", "hashPath": "sqlitepclraw.core.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-zibGtku8M4Eea1R3ZCAxc86QbNvyEN17mAcQkvWKBuHvRpMiK2g5anG4R5Be7cWKSd1i6baYz8y4dMMAKcXKPg==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-lxCZarZdvAsMl2zw9bXHrXK6RxVhB4b23iTFhCOdHFhxfbsxLxWf+ocvswJwR/9Wh/E//ddMi+wJGqUKV7VwoA==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.2.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zaJsHfESQvJ11vbXnNlkrR46IaMULk/gHxYsJphzSF+07kTjPHv+Oc14w6QEOfo3Q4hqLJgStUaYB9DBl0TmWg==", "path": "system.text.json/6.0.0", "hashPath": "system.text.json.6.0.0.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"], "win-x64-aot": ["win-aot", "win-x64", "win", "aot", "any", "base"], "win10-x64": ["win10", "win81-x64", "win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win10-x64-aot": ["win10-aot", "win10-x64", "win10", "win81-x64-aot", "win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win7-x64": ["win7", "win-x64", "win", "any", "base"], "win7-x64-aot": ["win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win8-x64": ["win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win8-x64-aot": ["win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win81-x64": ["win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win81-x64-aot": ["win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"]}}
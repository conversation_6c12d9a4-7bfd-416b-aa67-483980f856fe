﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FinancialTracker.Migrations
{
    public partial class InitialCreate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Projects",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Status = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true, defaultValue: "Active"),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Projects", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Sites",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    DisplayName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    Status = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true, defaultValue: "Active"),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ContactEmail = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    ContactPhone = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Address = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Sites", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Commitments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Title = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    ProjectId = table.Column<int>(type: "INTEGER", nullable: false),
                    SiteId = table.Column<int>(type: "INTEGER", nullable: false),
                    Type = table.Column<string>(type: "TEXT", nullable: false),
                    AmountUSD = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ExchangeRate = table.Column<decimal>(type: "decimal(10,4)", nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    StartDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    EndDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    AttachedFilePath = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    AttachedFileName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Commitments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Commitments_Projects_ProjectId",
                        column: x => x.ProjectId,
                        principalTable: "Projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Commitments_Sites_SiteId",
                        column: x => x.SiteId,
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ProjectSites",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ProjectId = table.Column<int>(type: "INTEGER", nullable: false),
                    SiteId = table.Column<int>(type: "INTEGER", nullable: false),
                    AssignedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Status = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true, defaultValue: "Active"),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    EstimatedBudget = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    ActualCost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    StartDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    EndDate = table.Column<DateTime>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProjectSites", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProjectSites_Projects_ProjectId",
                        column: x => x.ProjectId,
                        principalTable: "Projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ProjectSites_Sites_SiteId",
                        column: x => x.SiteId,
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Invoices",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    InvoiceNumber = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    ProjectId = table.Column<int>(type: "INTEGER", nullable: false),
                    CommitmentId = table.Column<int>(type: "INTEGER", nullable: true),
                    SiteId = table.Column<int>(type: "INTEGER", nullable: false),
                    AmountUSD = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    InvoiceDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsPaid = table.Column<bool>(type: "INTEGER", nullable: false),
                    PaidDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    PaidAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, defaultValue: 0m),
                    Type = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true, defaultValue: "Task"),
                    AttachedFilePath = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    AttachedFileName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Invoices", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Invoices_Commitments_CommitmentId",
                        column: x => x.CommitmentId,
                        principalTable: "Commitments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_Invoices_Projects_ProjectId",
                        column: x => x.ProjectId,
                        principalTable: "Projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Invoices_Sites_SiteId",
                        column: x => x.SiteId,
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });



            migrationBuilder.CreateTable(
                name: "Replies",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    InvoiceId = table.Column<int>(type: "INTEGER", nullable: false),
                    CommitmentId = table.Column<int>(type: "INTEGER", nullable: false),
                    Title = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    AttachedFilePath = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    AttachedFileName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Replies", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Replies_Commitments_CommitmentId",
                        column: x => x.CommitmentId,
                        principalTable: "Commitments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Replies_Invoices_InvoiceId",
                        column: x => x.InvoiceId,
                        principalTable: "Invoices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "Projects",
                columns: new[] { "Id", "CreatedDate", "Description", "Name", "Status" },
                values: new object[] { 1, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "University Management System", "UMS", "Active" });

            migrationBuilder.InsertData(
                table: "Projects",
                columns: new[] { "Id", "CreatedDate", "Description", "Name", "Status" },
                values: new object[] { 2, new DateTime(2024, 1, 6, 0, 0, 0, 0, DateTimeKind.Unspecified), "Banking and Finance System", "BNG", "Active" });

            migrationBuilder.InsertData(
                table: "Projects",
                columns: new[] { "Id", "CreatedDate", "Description", "Name", "Status" },
                values: new object[] { 3, new DateTime(2024, 1, 11, 0, 0, 0, 0, DateTimeKind.Unspecified), "Authentication and Authorization", "AAA", "Active" });

            migrationBuilder.InsertData(
                table: "Projects",
                columns: new[] { "Id", "CreatedDate", "Description", "Name", "Status" },
                values: new object[] { 4, new DateTime(2024, 1, 16, 0, 0, 0, 0, DateTimeKind.Unspecified), "Network Time Protocol", "NTP", "Active" });

            migrationBuilder.InsertData(
                table: "Projects",
                columns: new[] { "Id", "CreatedDate", "Description", "Name", "Status" },
                values: new object[] { 5, new DateTime(2024, 1, 21, 0, 0, 0, 0, DateTimeKind.Unspecified), "High Performance Business Exchange", "HPBX", "Active" });

            migrationBuilder.InsertData(
                table: "Projects",
                columns: new[] { "Id", "CreatedDate", "Description", "Name", "Status" },
                values: new object[] { 6, new DateTime(2024, 1, 26, 0, 0, 0, 0, DateTimeKind.Unspecified), "Enterprise Resource Planning System", "ERP System", "Active" });

            migrationBuilder.InsertData(
                table: "Projects",
                columns: new[] { "Id", "CreatedDate", "Description", "Name", "Status" },
                values: new object[] { 7, new DateTime(2024, 1, 31, 0, 0, 0, 0, DateTimeKind.Unspecified), "Customer Relationship Management Platform", "CRM Platform", "Active" });

            migrationBuilder.InsertData(
                table: "Projects",
                columns: new[] { "Id", "CreatedDate", "Description", "Name", "Status" },
                values: new object[] { 8, new DateTime(2024, 2, 5, 0, 0, 0, 0, DateTimeKind.Unspecified), "E-Commerce Website and Mobile App", "E-Commerce", "Active" });

            migrationBuilder.InsertData(
                table: "Projects",
                columns: new[] { "Id", "CreatedDate", "Description", "Name", "Status" },
                values: new object[] { 9, new DateTime(2024, 2, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "Business Intelligence and Data Analytics Platform", "Data Analytics", "Active" });

            migrationBuilder.InsertData(
                table: "Projects",
                columns: new[] { "Id", "CreatedDate", "Description", "Name", "Status" },
                values: new object[] { 10, new DateTime(2024, 2, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), "Cross-Platform Mobile Application", "Mobile App", "Active" });

            migrationBuilder.InsertData(
                table: "Sites",
                columns: new[] { "Id", "Address", "ContactEmail", "ContactPhone", "CreatedDate", "Description", "DisplayName", "Name", "Status" },
                values: new object[] { 1, null, null, null, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "الموقع الرئيسي الأول", "الموقع الأول - NN1", "nn1", "Active" });

            migrationBuilder.InsertData(
                table: "Sites",
                columns: new[] { "Id", "Address", "ContactEmail", "ContactPhone", "CreatedDate", "Description", "DisplayName", "Name", "Status" },
                values: new object[] { 2, null, null, null, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "الموقع الرئيسي الثاني", "الموقع الثاني - NN2", "nn2", "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 1, null, new DateTime(2024, 1, 6, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 1, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 2, null, new DateTime(2024, 1, 6, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 1, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 3, null, new DateTime(2024, 1, 11, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 2, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 4, null, new DateTime(2024, 1, 11, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 2, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 5, null, new DateTime(2024, 1, 16, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 3, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 6, null, new DateTime(2024, 1, 16, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 3, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 7, null, new DateTime(2024, 1, 21, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 4, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 8, null, new DateTime(2024, 1, 21, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 4, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 9, null, new DateTime(2024, 1, 26, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 5, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 10, null, new DateTime(2024, 1, 26, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 5, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 11, null, new DateTime(2024, 1, 31, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 6, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 12, null, new DateTime(2024, 1, 31, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 6, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 13, null, new DateTime(2024, 2, 5, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 7, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 14, null, new DateTime(2024, 2, 5, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 7, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 15, null, new DateTime(2024, 2, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 8, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 16, null, new DateTime(2024, 2, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 8, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 17, null, new DateTime(2024, 2, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 9, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 18, null, new DateTime(2024, 2, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 9, 2, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 19, null, new DateTime(2024, 2, 20, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 10, 1, null, "Active" });

            migrationBuilder.InsertData(
                table: "ProjectSites",
                columns: new[] { "Id", "ActualCost", "AssignedDate", "EndDate", "EstimatedBudget", "Notes", "ProjectId", "SiteId", "StartDate", "Status" },
                values: new object[] { 20, null, new DateTime(2024, 2, 20, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, null, 10, 2, null, "Active" });

            migrationBuilder.CreateIndex(
                name: "IX_Commitments_ProjectId",
                table: "Commitments",
                column: "ProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_Commitments_SiteId",
                table: "Commitments",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_CommitmentId",
                table: "Invoices",
                column: "CommitmentId");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_ProjectId",
                table: "Invoices",
                column: "ProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_SiteId",
                table: "Invoices",
                column: "SiteId");



            migrationBuilder.CreateIndex(
                name: "IX_ProjectSites_ProjectId_SiteId",
                table: "ProjectSites",
                columns: new[] { "ProjectId", "SiteId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ProjectSites_SiteId",
                table: "ProjectSites",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_Replies_CommitmentId",
                table: "Replies",
                column: "CommitmentId");

            migrationBuilder.CreateIndex(
                name: "IX_Replies_InvoiceId",
                table: "Replies",
                column: "InvoiceId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {


            migrationBuilder.DropTable(
                name: "ProjectSites");

            migrationBuilder.DropTable(
                name: "Replies");

            migrationBuilder.DropTable(
                name: "Invoices");

            migrationBuilder.DropTable(
                name: "Commitments");

            migrationBuilder.DropTable(
                name: "Projects");

            migrationBuilder.DropTable(
                name: "Sites");
        }
    }
}

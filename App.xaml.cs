using System;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using FinancialTracker.Data;
using FinancialTracker.Services;

namespace FinancialTracker
{
    public partial class App : Application
    {
        public static IDataService DataService { get; private set; } = null!;
        public static IFileService FileService { get; private set; } = null!;

        protected override async void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            try
            {
                // Initialize database and services
                var context = new FinancialContext();

                // Ensure database is created
                await context.Database.EnsureCreatedAsync();

                DataService = new DataService(context);
                FileService = new FileService();

                // Show main window
                var mainWindow = new MainWindow();
                mainWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error starting application: {ex.Message}\n\nInner Exception: {ex.InnerException?.Message}",
                    "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }
    }
}

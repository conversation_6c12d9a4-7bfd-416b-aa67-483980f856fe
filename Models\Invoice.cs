#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FinancialTracker.Helpers;

namespace FinancialTracker.Models
{
    public class Invoice
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string InvoiceNumber { get; set; } = string.Empty;
        
        [Required]
        public int ProjectId { get; set; }

        public int? CommitmentId { get; set; }
        
        private decimal _amountUSD;

        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountUSD
        {
            get => MathHelper.RoundCustom(_amountUSD);
            set => _amountUSD = value;
        }
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        public DateTime? SignatureDate { get; set; }
        
        [Column(TypeName = "decimal(10,4)")]
        public decimal ExchangeRate { get; set; } = 1.0m;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public bool IsPaid { get; set; } = false;
        
        public DateTime? PaidDate { get; set; }

        // Partial payment support
        private decimal _paidAmount = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount
        {
            get => MathHelper.RoundCustom(_paidAmount);
            set => _paidAmount = value;
        }

        [MaxLength(50)]
        public string Type { get; set; } = "مهمات سوفت وير";

        [MaxLength(500)]
        public string? AttachedFilePath { get; set; }

        [MaxLength(100)]
        public string? AttachedFileName { get; set; }

        // Letter attachment properties
        [MaxLength(500)]
        public string? LetterFilePath { get; set; }

        [MaxLength(100)]
        public string? LetterFileName { get; set; }
        
        // Navigation properties
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;

        [ForeignKey("CommitmentId")]
        public virtual Commitment? Commitment { get; set; }

        public virtual ICollection<Reply> Replies { get; set; } = new List<Reply>();

        // Computed properties
        [NotMapped]
        public decimal RemainingAmount => MathHelper.RoundCustom(AmountUSD - PaidAmount);

        [NotMapped]
        public bool IsPartiallyPaid => PaidAmount > 0 && PaidAmount < AmountUSD;

        [NotMapped]
        public bool IsFullyPaid => PaidAmount >= AmountUSD;



        // خاصية لعرض النوع بشكل صحيح
        [NotMapped]
        public string TypeDisplay => GetTypeDisplay();

        private string GetTypeDisplay()
        {
            // إذا كان النوع رقم، نحوله إلى نص
            if (int.TryParse(Type, out int typeNumber))
            {
                return typeNumber switch
                {
                    1 => "مهمات سوفت وير",
                    2 => "مهمات هارد وير",
                    3 => "خدمات",
                    _ => "أخرى"
                };
            }

            // إذا كان النوع نص، نعيده كما هو أو نحوله للعربية
            return Type.ToLower() switch
            {
                "task" => "مهمات",
                "service" => "خدمات",
                _ => Type
            };
        }

        [NotMapped]
        public string PaymentStatus
        {
            get
            {
                if (PaidAmount == 0) return "غير مدفوع";
                if (PaidAmount >= AmountUSD) return "مدفوع بالكامل";
                return "مدفوع جزئياً";
            }
        }

        [NotMapped]
        public bool IsSigned => SignatureDate.HasValue;

        [NotMapped]
        public string SignatureStatus => IsSigned ? "Signed" : "Unsigned";
    }
}

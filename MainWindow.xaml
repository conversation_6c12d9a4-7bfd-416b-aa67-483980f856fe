<Window x:Class="FinancialTracker.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:FinancialTracker"
        Title="Financial Tracker"
        Height="800"
        Width="1400"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Top Bar -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Finance" Width="32" Height="32" Margin="0,0,8,0"/>
                    <TextBlock Text="Financial Tracker" FontSize="20" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="DashboardButton" Content="Dashboard" Style="{StaticResource MaterialDesignFlatButton}"
                            Foreground="White" Margin="8,0" Click="DashboardButton_Click"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1">
            <Grid Margin="16">
                <!-- Dashboard Content -->
                <Grid x:Name="DashboardContent" Visibility="Visible">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Header -->
                    <TextBlock Grid.Row="0" Text="Projects Overview" FontSize="20" FontWeight="Bold" Margin="0,0,0,12"/>

                    <!-- PO Financial Summary Cards -->
                    <Grid Grid.Row="1" Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:Card Grid.Column="0" Margin="6" Padding="16" Background="#E3F2FD" MinHeight="90">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="FolderMultiple" Width="24" Height="24" Foreground="#1976D2" Margin="0,0,0,8"/>
                                <TextBlock Text="Total Projects" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalProjectsText" Text="0" FontSize="24" FontWeight="Bold" Foreground="#1976D2" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="1" Margin="6" Padding="16" Background="#E8F5E8" MinHeight="90">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="24" Height="24" Foreground="#388E3C" Margin="0,0,0,8"/>
                                <TextBlock Text="Total PO Value" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalPOValueText" Text="$0" FontSize="24" FontWeight="Bold" Foreground="#388E3C" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="2" Margin="6" Padding="16" Background="#FFF3E0" MinHeight="90">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="TrendingUp" Width="24" Height="24" Foreground="#F57C00" Margin="0,0,0,8"/>
                                <TextBlock Text="Total Spent" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalSpentText" Text="$0" FontSize="24" FontWeight="Bold" Foreground="#F57C00" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="3" Margin="6" Padding="16" Background="#FFEBEE" MinHeight="90">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="TrendingDown" Width="24" Height="24" Foreground="#D32F2F" Margin="0,0,0,8"/>
                                <TextBlock Text="Total Remaining" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalRemainingText" Text="$0" FontSize="24" FontWeight="Bold" Foreground="#D32F2F" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>





                    <!-- Projects Details - Takes remaining space -->
                    <materialDesign:Card Grid.Row="3" Margin="4" Padding="12">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <!-- Header - Minimal design -->
                            <Grid Grid.Row="0" Margin="0,0,0,4">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Projects Dashboard" FontSize="16" FontWeight="Bold"
                                          VerticalAlignment="Center"/>
                                <Button Grid.Column="1" Style="{StaticResource MaterialDesignRaisedButton}"
                                        FontSize="11" Padding="10,4" Click="AddProjectButton_Click">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Plus" Width="12" Height="12" Margin="0,0,3,0"/>
                                        <TextBlock Text="Add"/>
                                    </StackPanel>
                                </Button>
                            </Grid>

                            <!-- Search Section - Ultra compact -->
                            <Grid Grid.Row="1" Margin="0,0,0,4">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Search Box -->
                                <Grid Grid.Column="0">
                                    <TextBox x:Name="SearchTextBox" materialDesign:HintAssist.Hint="Search..."
                                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                             Padding="30,6,6,6" FontSize="11" Height="30"
                                             TextChanged="SearchTextBox_TextChanged"/>
                                    <materialDesign:PackIcon Kind="Magnify" Width="14" Height="14"
                                                           HorizontalAlignment="Left" VerticalAlignment="Center"
                                                           Margin="8,0,0,0" Opacity="0.6" />
                                </Grid>

                                <!-- Clear Button -->
                                <Button Grid.Column="1" x:Name="ClearFiltersButton"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Margin="6,0" Padding="6,3" FontSize="10" Height="30"
                                        Click="ClearFiltersButton_Click">
                                    <materialDesign:PackIcon Kind="FilterRemove" Width="12" Height="12"/>
                                </Button>

                                <!-- Refresh Button -->
                                <Button Grid.Column="2" x:Name="RefreshButton" Style="{StaticResource MaterialDesignIconButton}"
                                        ToolTip="Refresh" Width="30" Height="30" Click="RefreshButton_Click">
                                    <materialDesign:PackIcon Kind="Refresh" Width="14" Height="14"/>
                                </Button>
                            </Grid>
                            <!-- Projects Cards Layout -->
                            <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                                <ItemsControl x:Name="ProjectsCardsControl">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <UniformGrid Columns="1"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <!-- Project Card -->
                                            <materialDesign:Card Margin="0,0,0,8" Padding="0">
                                                <Grid>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                    </Grid.RowDefinitions>

                                                    <!-- Header Section -->
                                                    <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="16,12">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                            </Grid.ColumnDefinitions>

                                                            <!-- Project Name -->
                                                            <TextBlock Grid.Column="0" Text="{Binding Name}" FontSize="16" FontWeight="Bold"
                                                                     VerticalAlignment="Center" Foreground="White"/>

                                                            <!-- Status Badge -->
                                                            <Border Grid.Column="1" Background="White" CornerRadius="12" Padding="8,4" Margin="0,0,12,0">
                                                                <TextBlock Text="{Binding Status}" FontSize="11" FontWeight="Medium"
                                                                         Foreground="#2C3E50" HorizontalAlignment="Center"/>
                                                            </Border>

                                                            <!-- Action Buttons -->
                                                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                        ToolTip="Details" Margin="2,0" Foreground="White"
                                                                        Click="ViewProjectDetailsButton_Click">
                                                                    <materialDesign:PackIcon Kind="Eye" Width="18" Height="18"/>
                                                                </Button>
                                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                        ToolTip="Edit" Margin="2,0" Foreground="White"
                                                                        Click="EditProjectButton_Click">
                                                                    <materialDesign:PackIcon Kind="Pencil" Width="18" Height="18"/>
                                                                </Button>
                                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                        ToolTip="Files" Margin="2,0" Foreground="White"
                                                                        Click="ViewProjectFilesButton_Click">
                                                                    <materialDesign:PackIcon Kind="Folder" Width="18" Height="18"/>
                                                                </Button>
                                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                        ToolTip="Delete" Margin="2,0" Foreground="White"
                                                                        Click="DeleteProjectButton_Click">
                                                                    <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                                                </Button>
                                                            </StackPanel>
                                                        </Grid>
                                                    </materialDesign:ColorZone>

                                                    <!-- Content Section -->
                                                    <Grid Grid.Row="1" Padding="16">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="*"/>
                                                        </Grid.ColumnDefinitions>

                                                        <!-- PO Section -->
                                                        <Border Grid.Column="0" Background="#E3F2FD" CornerRadius="8" Padding="12" Margin="0,0,8,0">
                                                            <StackPanel>
                                                                <TextBlock Text="Purchase Orders" FontSize="12" FontWeight="Bold"
                                                                         Foreground="#1976D2" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                                                <Grid>
                                                                    <Grid.RowDefinitions>
                                                                        <RowDefinition Height="Auto"/>
                                                                        <RowDefinition Height="Auto"/>
                                                                        <RowDefinition Height="Auto"/>
                                                                    </Grid.RowDefinitions>
                                                                    <Grid.ColumnDefinitions>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                    </Grid.ColumnDefinitions>

                                                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Total:" FontSize="11" Margin="0,0,8,4"/>
                                                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding POAmount, StringFormat='{}{0:C0}'}"
                                                                             FontSize="13" FontWeight="Bold" Foreground="#1976D2" HorizontalAlignment="Right"/>

                                                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Spent:" FontSize="11" Margin="0,0,8,4"/>
                                                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding TotalSpent, StringFormat='{}{0:C0}'}"
                                                                             FontSize="13" FontWeight="Bold" Foreground="#F57C00" HorizontalAlignment="Right"/>

                                                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Remaining:" FontSize="11" Margin="0,0,8,0"/>
                                                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding PORemaining, StringFormat='{}{0:C0}'}"
                                                                             FontSize="13" FontWeight="Bold" Foreground="#D32F2F" HorizontalAlignment="Right"/>
                                                                </Grid>
                                                            </StackPanel>
                                                        </Border>

                                                        <!-- Tasks Section -->
                                                        <Border Grid.Column="1" Background="#E8F5E8" CornerRadius="8" Padding="12" Margin="4,0">
                                                            <StackPanel>
                                                                <TextBlock Text="Tasks &amp; Hardware" FontSize="12" FontWeight="Bold"
                                                                         Foreground="#4CAF50" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                                                <Grid>
                                                                    <Grid.RowDefinitions>
                                                                        <RowDefinition Height="Auto"/>
                                                                        <RowDefinition Height="Auto"/>
                                                                        <RowDefinition Height="Auto"/>
                                                                    </Grid.RowDefinitions>
                                                                    <Grid.ColumnDefinitions>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                    </Grid.ColumnDefinitions>

                                                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Total:" FontSize="11" Margin="0,0,8,4"/>
                                                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding TaskCommitmentsAmount, StringFormat='{}{0:C0}'}"
                                                                             FontSize="13" FontWeight="Bold" Foreground="#4CAF50" HorizontalAlignment="Right"/>

                                                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Spent:" FontSize="11" Margin="0,0,8,4"/>
                                                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding TasksSpent, StringFormat='{}{0:C0}'}"
                                                                             FontSize="13" FontWeight="Bold" Foreground="#FF9800" HorizontalAlignment="Right"/>

                                                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Remaining:" FontSize="11" Margin="0,0,8,0"/>
                                                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding TasksRemaining, StringFormat='{}{0:C0}'}"
                                                                             FontSize="13" FontWeight="Bold" Foreground="#E91E63" HorizontalAlignment="Right"/>
                                                                </Grid>
                                                            </StackPanel>
                                                        </Border>

                                                        <!-- Services Section -->
                                                        <Border Grid.Column="2" Background="#F3E5F5" CornerRadius="8" Padding="12" Margin="8,0,0,0">
                                                            <StackPanel>
                                                                <TextBlock Text="Services" FontSize="12" FontWeight="Bold"
                                                                         Foreground="#9C27B0" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                                                <Grid>
                                                                    <Grid.RowDefinitions>
                                                                        <RowDefinition Height="Auto"/>
                                                                        <RowDefinition Height="Auto"/>
                                                                        <RowDefinition Height="Auto"/>
                                                                    </Grid.RowDefinitions>
                                                                    <Grid.ColumnDefinitions>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                    </Grid.ColumnDefinitions>

                                                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Total:" FontSize="11" Margin="0,0,8,4"/>
                                                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding ServiceCommitmentsAmount, StringFormat='{}{0:C0}'}"
                                                                             FontSize="13" FontWeight="Bold" Foreground="#9C27B0" HorizontalAlignment="Right"/>

                                                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Spent:" FontSize="11" Margin="0,0,8,4"/>
                                                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding ServicesSpent, StringFormat='{}{0:C0}'}"
                                                                             FontSize="13" FontWeight="Bold" Foreground="#FF5722" HorizontalAlignment="Right"/>

                                                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Remaining:" FontSize="11" Margin="0,0,8,0"/>
                                                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding ServicesRemaining, StringFormat='{}{0:C0}'}"
                                                                             FontSize="13" FontWeight="Bold" Foreground="#795548" HorizontalAlignment="Right"/>
                                                                </Grid>
                                                            </StackPanel>
                                                        </Border>
                                                    </Grid>
                                                </Grid>
                                            </materialDesign:Card>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </Grid>
                    </materialDesign:Card>
                </Grid>




            </Grid>
        </ScrollViewer>

        <!-- Status Bar -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="16,4">
            <Grid>
                <TextBlock Text="Ready" VerticalAlignment="Center"/>
                <TextBlock Text="v1.0.0" HorizontalAlignment="Right" VerticalAlignment="Center"/>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</Window>

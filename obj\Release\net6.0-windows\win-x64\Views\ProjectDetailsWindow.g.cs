﻿#pragma checksum "..\..\..\..\..\Views\ProjectDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E30147650483E7E81F6E6F931D0630B260189D8D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FinancialTracker;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FinancialTracker {
    
    
    /// <summary>
    /// ProjectDetailsWindow
    /// </summary>
    public partial class ProjectDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 34 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectNameText;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectNameDetail;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectStatusDetail;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectCreatedDetail;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectDescriptionDetail;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PODateDetail;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock POAmountDetail;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SpentFromPODetail;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingFromPODetail;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock POTotalValueText;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock POSpentText;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PORemainingText;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TasksTotalValueText;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TasksSpentText;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TasksRemainingText;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ServicesTotalValueText;
        
        #line default
        #line hidden
        
        
        #line 273 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ServicesSpentText;
        
        #line default
        #line hidden
        
        
        #line 281 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ServicesRemainingText;
        
        #line default
        #line hidden
        
        
        #line 338 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoiceSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 348 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearInvoiceFilterButton;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoicesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 446 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CommitmentSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 456 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearCommitmentFilterButton;
        
        #line default
        #line hidden
        
        
        #line 469 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid CommitmentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 512 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FinancialTracker;component/views/projectdetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ProjectNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 39 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 43 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditProjectButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 45 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ProjectNameDetail = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.ProjectStatusDetail = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.ProjectCreatedDetail = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.ProjectDescriptionDetail = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.PODateDetail = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.POAmountDetail = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.SpentFromPODetail = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.RemainingFromPODetail = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.POTotalValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.POSpentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.PORemainingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.TasksTotalValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.TasksSpentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.TasksRemainingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.ServicesTotalValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.ServicesSpentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.ServicesRemainingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            
            #line 324 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.InvoiceSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 341 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            this.InvoiceSearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.InvoiceSearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 24:
            this.ClearInvoiceFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 351 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            this.ClearInvoiceFilterButton.Click += new System.Windows.RoutedEventHandler(this.ClearInvoiceFilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.InvoicesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 31:
            
            #line 432 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddCommitmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.CommitmentSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 449 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            this.CommitmentSearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CommitmentSearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 33:
            this.ClearCommitmentFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 459 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            this.ClearCommitmentFilterButton.Click += new System.Windows.RoutedEventHandler(this.ClearCommitmentFilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            this.CommitmentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 40:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 26:
            
            #line 380 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            case 27:
            
            #line 382 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            case 28:
            
            #line 384 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenInvoiceLetterButton_Click);
            
            #line default
            #line hidden
            break;
            case 29:
            
            #line 387 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenInvoiceFileButton_Click);
            
            #line default
            #line hidden
            break;
            case 30:
            
            #line 390 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            case 35:
            
            #line 486 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditCommitmentButton_Click);
            
            #line default
            #line hidden
            break;
            case 36:
            
            #line 488 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyCommitmentButton_Click);
            
            #line default
            #line hidden
            break;
            case 37:
            
            #line 490 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewCommitmentInvoicesButton_Click);
            
            #line default
            #line hidden
            break;
            case 38:
            
            #line 492 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenCommitmentFileButton_Click);
            
            #line default
            #line hidden
            break;
            case 39:
            
            #line 495 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteCommitmentButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}


#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;
using FinancialTracker.Helpers;

namespace FinancialTracker
{
    public partial class CommitmentDialog : Window
    {
        private Commitment? _commitment;
        private bool _isEdit;
        private string _selectedFilePath = string.Empty;
        private int? _preSelectedProjectId;
        private List<Invoice> _availableInvoices = new List<Invoice>();
        private List<Invoice> _linkedInvoices = new List<Invoice>();


        public CommitmentDialog(Commitment? commitment = null, int? projectId = null)
        {
            InitializeComponent();
            _commitment = commitment;
            _isEdit = commitment != null;
            _preSelectedProjectId = projectId;

            LoadData();
            _ = LoadAvailableInvoices();

            if (_isEdit && _commitment != null)
            {
                Title = "Edit Commitment";
                PopulateFields();
                _ = LoadLinkedInvoices();
            }
            else
            {
                Title = "Add New Commitment";

                // Set default creation date to today
                CreatedDatePicker.SelectedDate = DateTime.Today;

                // Pre-select project if provided
                if (_preSelectedProjectId.HasValue)
                {
                    ProjectComboBox.SelectedValue = _preSelectedProjectId.Value;
                }
            }
        }

        private async void LoadData()
        {
            try
            {
                var projects = await App.DataService.GetProjectsAsync();
                ProjectComboBox.ItemsSource = projects;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private void TypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                if (selectedItem.Content?.ToString() == "أخرى")
                {
                    CustomTypeTextBox.Visibility = Visibility.Visible;
                }
                else
                {
                    CustomTypeTextBox.Visibility = Visibility.Collapsed;
                    CustomTypeTextBox.Text = "";
                }
            }
        }

        private string GetSelectedType()
        {
            if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var selectedType = selectedItem.Content?.ToString() ?? "مهمات سوفت وير";
                if (selectedType == "أخرى")
                {
                    return !string.IsNullOrWhiteSpace(CustomTypeTextBox.Text) ? CustomTypeTextBox.Text.Trim() : "أخرى";
                }
                return selectedType;
            }
            return "مهمات سوفت وير";
        }

        private void PopulateFields()
        {
            if (_commitment == null) return;

            TitleTextBox.Text = _commitment.Title;
            ProjectComboBox.SelectedValue = _commitment.ProjectId;

            // Set type
            bool typeFound = false;
            foreach (ComboBoxItem item in TypeComboBox.Items)
            {
                if (item.Content?.ToString() == _commitment.Type)
                {
                    TypeComboBox.SelectedItem = item;
                    typeFound = true;
                    break;
                }
            }

            // If type not found in predefined options, select "أخرى" and set custom text
            if (!typeFound && !string.IsNullOrEmpty(_commitment.Type))
            {
                foreach (ComboBoxItem item in TypeComboBox.Items)
                {
                    if (item.Content?.ToString() == "أخرى")
                    {
                        TypeComboBox.SelectedItem = item;
                        CustomTypeTextBox.Text = _commitment.Type;
                        CustomTypeTextBox.Visibility = Visibility.Visible;
                        break;
                    }
                }
            }

            AmountTextBox.Text = _commitment.AmountUSD.ToString("F0");
            ExchangeRateTextBox.Text = _commitment.ExchangeRate.ToString("F4");
            StartDatePicker.SelectedDate = _commitment.StartDate;
            EndDatePicker.SelectedDate = _commitment.EndDate;
            DescriptionTextBox.Text = _commitment.Description;
            CreatedDatePicker.SelectedDate = _commitment.CreatedDate;
            IsActiveCheckBox.IsChecked = _commitment.IsActive;

            if (!string.IsNullOrEmpty(_commitment.AttachedFileName))
            {
                AttachedFileTextBox.Text = _commitment.AttachedFileName;
                OpenFileButton.IsEnabled = true;
            }
        }

        private void SelectFileButton_Click(object sender, RoutedEventArgs e)
        {
            var filter = "PDF Files (*.pdf)|*.pdf|Word Documents (*.doc;*.docx)|*.doc;*.docx|All Files (*.*)|*.*";
            _selectedFilePath = App.FileService.SelectFile(filter);
            
            if (!string.IsNullOrEmpty(_selectedFilePath))
            {
                AttachedFileTextBox.Text = System.IO.Path.GetFileName(_selectedFilePath);
                OpenFileButton.IsEnabled = true;
            }
        }

        private async void OpenFileButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(_selectedFilePath))
                {
                    await App.FileService.OpenFileAsync(_selectedFilePath);
                }
                else if (_commitment != null && !string.IsNullOrEmpty(_commitment.AttachedFilePath))
                {
                    await App.FileService.OpenFileAsync(_commitment.AttachedFilePath);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening file: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(TitleTextBox.Text))
                {
                    MessageBox.Show("Commitment title is required.", "Validation Error", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (ProjectComboBox.SelectedValue == null)
                {
                    MessageBox.Show("Please select a project.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }



                if (TypeComboBox.SelectedIndex == -1)
                {
                    MessageBox.Show("Please select a commitment type.", "Validation Error", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!decimal.TryParse(AmountTextBox.Text, out decimal amount))
                {
                    MessageBox.Show("Please enter a valid amount.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // تطبيق التقريب المخصص على المبلغ
                amount = MathHelper.RoundCustom(amount);

                if (!decimal.TryParse(ExchangeRateTextBox.Text, out decimal exchangeRate))
                {
                    MessageBox.Show("Please enter a valid exchange rate.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (CreatedDatePicker.SelectedDate == null)
                {
                    MessageBox.Show("يرجى اختيار تاريخ الإنشاء.", "خطأ في التحقق",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                string? attachedFilePath = null;
                string? attachedFileName = null;

                // Handle file attachment
                if (!string.IsNullOrEmpty(_selectedFilePath))
                {
                    attachedFileName = System.IO.Path.GetFileName(_selectedFilePath);
                    attachedFilePath = await App.FileService.SaveFileAsync(_selectedFilePath, "commitment", attachedFileName);
                }

                if (_isEdit && _commitment != null)
                {
                    _commitment.Title = TitleTextBox.Text.Trim();
                    _commitment.ProjectId = (int)ProjectComboBox.SelectedValue;
                    _commitment.Type = GetSelectedType();
                    _commitment.AmountUSD = amount;
                    _commitment.ExchangeRate = exchangeRate;
                    _commitment.StartDate = StartDatePicker.SelectedDate;
                    _commitment.EndDate = EndDatePicker.SelectedDate;
                    _commitment.Description = DescriptionTextBox.Text.Trim();
                    _commitment.CreatedDate = CreatedDatePicker.SelectedDate.Value;
                    _commitment.IsActive = IsActiveCheckBox.IsChecked ?? true;
                    
                    if (attachedFilePath != null)
                    {
                        _commitment.AttachedFilePath = attachedFilePath;
                        _commitment.AttachedFileName = attachedFileName;
                    }
                    
                    await App.DataService.UpdateCommitmentAsync(_commitment);
                }
                else
                {
                    var newCommitment = new Commitment
                    {
                        Title = TitleTextBox.Text.Trim(),
                        ProjectId = (int)ProjectComboBox.SelectedValue,

                        Type = GetSelectedType(),
                        AmountUSD = amount,
                        ExchangeRate = exchangeRate,
                        StartDate = StartDatePicker.SelectedDate,
                        EndDate = EndDatePicker.SelectedDate,
                        Description = DescriptionTextBox.Text.Trim(),
                        IsActive = IsActiveCheckBox.IsChecked ?? true,
                        AttachedFilePath = attachedFilePath,
                        AttachedFileName = attachedFileName,
                        CreatedDate = CreatedDatePicker.SelectedDate.Value
                    };
                    
                    await App.DataService.AddCommitmentAsync(newCommitment);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving commitment: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        // Invoice linking functionality
        private async System.Threading.Tasks.Task LoadAvailableInvoices()
        {
            try
            {
                if (ProjectComboBox.SelectedValue is int projectId)
                {
                    var allInvoices = await App.DataService.GetInvoicesAsync();
                    _availableInvoices = allInvoices.Where(i => i.ProjectId == projectId && i.CommitmentId == null).ToList();
                    AvailableInvoicesComboBox.ItemsSource = _availableInvoices;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading invoices: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadLinkedInvoices()
        {
            try
            {
                if (_commitment != null)
                {
                    var allInvoices = await App.DataService.GetInvoicesAsync();
                    _linkedInvoices = allInvoices.Where(i => i.CommitmentId == _commitment.Id).ToList();
                    LinkedInvoicesListBox.ItemsSource = _linkedInvoices;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading linked invoices: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ProjectComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            await LoadAvailableInvoices();
        }



        private async void RefreshInvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadAvailableInvoices();
        }

        private async void LinkInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (AvailableInvoicesComboBox.SelectedItem is Invoice selectedInvoice && _commitment != null)
                {
                    // Update the invoice to link it to this commitment
                    selectedInvoice.CommitmentId = _commitment.Id;
                    await App.DataService.UpdateInvoiceAsync(selectedInvoice);

                    // Refresh the lists
                    await LoadAvailableInvoices();
                    await LoadLinkedInvoices();

                    MessageBox.Show("تم ربط الفاتورة بنجاح!", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else if (_commitment == null)
                {
                    MessageBox.Show("يجب حفظ الارتباط أولاً قبل ربط الفواتير.", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
                else
                {
                    MessageBox.Show("يرجى اختيار فاتورة للربط.", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error linking invoice: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void UnlinkInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Invoice invoice)
                {
                    var result = MessageBox.Show($"هل تريد إلغاء ربط الفاتورة '{invoice.InvoiceNumber}'؟",
                        "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // Remove the link
                        invoice.CommitmentId = null;
                        await App.DataService.UpdateInvoiceAsync(invoice);

                        // Refresh the lists
                        await LoadAvailableInvoices();
                        await LoadLinkedInvoices();

                        MessageBox.Show("تم إلغاء ربط الفاتورة بنجاح!", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error unlinking invoice: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}

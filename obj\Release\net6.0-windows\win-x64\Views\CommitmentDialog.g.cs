﻿#pragma checksum "..\..\..\..\..\Views\CommitmentDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DD4244DFAFC907AEA7F790B0D50BE12A757886A9"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FinancialTracker;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FinancialTracker {
    
    
    /// <summary>
    /// CommitmentDialog
    /// </summary>
    public partial class CommitmentDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 25 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TitleTextBox;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProjectComboBox;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CustomTypeTextBox;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExchangeRateTextBox;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker StartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker EndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker CreatedDatePicker;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AvailableInvoicesComboBox;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LinkInvoiceButton;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshInvoicesButton;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox LinkedInvoicesListBox;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AttachedFileTextBox;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectFileButton;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\..\Views\CommitmentDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenFileButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.35.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FinancialTracker;component/views/commitmentdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\CommitmentDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.35.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.ProjectComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 36 "..\..\..\..\..\Views\CommitmentDialog.xaml"
            this.ProjectComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ProjectComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.TypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 45 "..\..\..\..\..\Views\CommitmentDialog.xaml"
            this.TypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TypeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CustomTypeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.AmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.ExchangeRateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.StartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 8:
            this.EndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 9:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.CreatedDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 11:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.AvailableInvoicesComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.LinkInvoiceButton = ((System.Windows.Controls.Button)(target));
            
            #line 126 "..\..\..\..\..\Views\CommitmentDialog.xaml"
            this.LinkInvoiceButton.Click += new System.Windows.RoutedEventHandler(this.LinkInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.RefreshInvoicesButton = ((System.Windows.Controls.Button)(target));
            
            #line 133 "..\..\..\..\..\Views\CommitmentDialog.xaml"
            this.RefreshInvoicesButton.Click += new System.Windows.RoutedEventHandler(this.RefreshInvoicesButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.LinkedInvoicesListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 17:
            this.AttachedFileTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.SelectFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 194 "..\..\..\..\..\Views\CommitmentDialog.xaml"
            this.SelectFileButton.Click += new System.Windows.RoutedEventHandler(this.SelectFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.OpenFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 202 "..\..\..\..\..\Views\CommitmentDialog.xaml"
            this.OpenFileButton.Click += new System.Windows.RoutedEventHandler(this.OpenFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 215 "..\..\..\..\..\Views\CommitmentDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 219 "..\..\..\..\..\Views\CommitmentDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.35.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 16:
            
            #line 162 "..\..\..\..\..\Views\CommitmentDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.UnlinkInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

